{"timestamp": "2025-06-16T15:12:32.457389", "match_id": "h2ivboJ5", "match": {"event_id": "h2ivboJ5", "start_time": "2025-06-17T18:00:00", "sport_id": 1, "league_name": "WORLD: FIFA Club World Cup", "country_id": 8, "country_name": "", "home_team": {"name": "Ulsan HD", "short_name": "ULS", "image_url": ""}, "away_team": {"name": "Mamelodi Sundowns", "short_name": "MAM", "image_url": ""}, "status": "1", "home_score": "", "away_score": "", "current_result": "", "match_url": "https://www.flashscore.com/match/h2ivboJ5/#/match-summary", "league_index": 75, "tournament_hash": "KOtwQCtI"}, "head_to_head": {"overall_home": [{"home_team": "Jeonbuk", "away_team": "Ulsan HD", "score": "3:1", "result": "LOST", "date": ""}, {"home_team": "Gwangju FC", "away_team": "Ulsan HD", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "<PERSON><PERSON><PERSON><PERSON>", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Gangwon", "away_team": "Ulsan HD", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Incheon", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Jeju SK", "away_team": "Ulsan HD", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Pohang", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gwangju FC", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON><PERSON>", "away_team": "Ulsan HD", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Anyang", "away_team": "Ulsan HD", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gangwon", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "Daegu", "away_team": "Ulsan HD", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Seoul", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Daejeon", "score": "2:3", "result": "LOST", "date": ""}, {"home_team": "Pohang", "away_team": "Ulsan HD", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Suwon FC", "away_team": "Ulsan HD", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Jeju SK", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Jeonbuk", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Daejeon", "away_team": "Ulsan HD", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Anyang", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Ulsan HD", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Lokomotiv Moscow", "away_team": "Ulsan HD", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "<PERSON><PERSON><PERSON>", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "FC Rapid Bucuresti", "away_team": "Ulsan HD", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Shanghai Shenhua", "away_team": "Ulsan HD", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Pohang", "away_team": "Ulsan HD", "score": "3:1", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Shanghai Port", "score": "1:3", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Suwon FC", "score": "4:2", "result": "WIN", "date": ""}, {"home_team": "Seoul", "away_team": "Ulsan HD", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Johor DT", "away_team": "Ulsan HD", "score": "3:0", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gangwon", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Pohang", "away_team": "Ulsan HD", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Vissel Kobe", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON><PERSON>", "away_team": "Ulsan HD", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "<PERSON><PERSON><PERSON><PERSON>", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Yokohama F. Marinos", "away_team": "Ulsan HD", "score": "4:0", "result": "LOST", "date": ""}, {"home_team": "Daejeon", "away_team": "Ulsan HD", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Incheon", "away_team": "Ulsan HD", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Kawasaki Frontale", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gangwon", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Pohang", "score": "5:4", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gwangju FC", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Gwangju FC", "away_team": "Ulsan HD", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Gwangju FC", "away_team": "Ulsan HD", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Suwon FC", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Daegu", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Jeju SK", "away_team": "Ulsan HD", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Jeonbuk", "away_team": "Ulsan HD", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Incheon", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Seoul", "score": "1:0", "result": "WIN", "date": ""}], "overall_away": [{"home_team": "Pyramids", "away_team": "Mamelodi Sundowns", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Pyramids", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "<PERSON><PERSON><PERSON>", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Chippa Utd.", "away_team": "Mamelodi Sundowns", "score": "0:3", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Stellenbosch", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Chippa Utd.", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Cape Town City", "away_team": "Mamelodi Sundowns", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "<PERSON>", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON>", "away_team": "Mamelodi Sundowns", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "<PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Kaizer Chiefs", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "Esperance Tunis", "away_team": "Mamelodi Sundowns", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Esperance Tunis", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Orlando Pirates", "away_team": "Mamelodi Sundowns", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "AmaZulu", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Golden Arrows", "away_team": "Mamelodi Sundowns", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Kaizer Chiefs", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "2:4", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "TS Galaxy", "score": "4:1", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "1:3", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "<PERSON><PERSON><PERSON>", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "TS Galaxy", "away_team": "Mamelodi Sundowns", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Orlando Pirates", "score": "4:1", "result": "WIN", "date": ""}, {"home_team": "Supersport Utd", "away_team": "Mamelodi Sundowns", "score": "0:3", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Golden Arrows", "score": "4:0", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Siban<PERSON>", "score": "5:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "FAR Rabat", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Raja Casablanca", "away_team": "Mamelodi Sundowns", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON>", "away_team": "Mamelodi Sundowns", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "AmaZulu", "away_team": "Mamelodi Sundowns", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Stellenbosch", "away_team": "Mamelodi Sundowns", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Raja Casablanca", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "FAR Rabat", "away_team": "Mamelodi Sundowns", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "<PERSON><PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Polokwane", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Kaizer Chiefs", "away_team": "Mamelodi Sundowns", "score": "0:4", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Cape Town City", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Polokwane", "away_team": "Mamelodi Sundowns", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Royal AM", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Golden Arrows", "score": "5:0", "result": "WIN", "date": ""}, {"home_team": "Kaizer Chiefs", "away_team": "Mamelodi Sundowns", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "<PERSON><PERSON><PERSON>", "score": "4:1", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Mbabane Swallows", "score": "4:0", "result": "WIN", "date": ""}, {"home_team": "Mamelodi Sundowns", "away_team": "Supersport Utd", "score": "2:0", "result": "WIN", "date": ""}], "head_to_head": [], "home_home": [{"home_team": "Ulsan HD", "away_team": "<PERSON><PERSON><PERSON><PERSON>", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Incheon", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Pohang", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gwangju FC", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gangwon", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Seoul", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Daejeon", "score": "2:3", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Jeju SK", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Jeonbuk", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Anyang", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "<PERSON><PERSON><PERSON>", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Shanghai Port", "score": "1:3", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Suwon FC", "score": "4:2", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gangwon", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Vissel Kobe", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "<PERSON><PERSON><PERSON><PERSON>", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Kawasaki Frontale", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gangwon", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Pohang", "score": "5:4", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Gwangju FC", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Suwon FC", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Daegu", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Incheon", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Ulsan HD", "away_team": "Seoul", "score": "1:0", "result": "WIN", "date": ""}], "away_away": [{"home_team": "Pyramids", "away_team": "Mamelodi Sundowns", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Chippa Utd.", "away_team": "Mamelodi Sundowns", "score": "0:3", "result": "WIN", "date": ""}, {"home_team": "Cape Town City", "away_team": "Mamelodi Sundowns", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON>", "away_team": "Mamelodi Sundowns", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Esperance Tunis", "away_team": "Mamelodi Sundowns", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Orlando Pirates", "away_team": "Mamelodi Sundowns", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Golden Arrows", "away_team": "Mamelodi Sundowns", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "2:4", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "1:3", "result": "WIN", "date": ""}, {"home_team": "TS Galaxy", "away_team": "Mamelodi Sundowns", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Supersport Utd", "away_team": "Mamelodi Sundowns", "score": "0:3", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Raja Casablanca", "away_team": "Mamelodi Sundowns", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON>", "away_team": "Mamelodi Sundowns", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "AmaZulu", "away_team": "Mamelodi Sundowns", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Stellenbosch", "away_team": "Mamelodi Sundowns", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "FAR Rabat", "away_team": "Mamelodi Sundowns", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Mamelodi Sundowns", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Kaizer Chiefs", "away_team": "Mamelodi Sundowns", "score": "0:4", "result": "WIN", "date": ""}, {"home_team": "Polokwane", "away_team": "Mamelodi Sundowns", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Kaizer Chiefs", "away_team": "Mamelodi Sundowns", "score": "1:2", "result": "WIN", "date": ""}]}, "betting_odds": null}