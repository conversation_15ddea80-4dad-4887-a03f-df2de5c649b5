{"timestamp": "2025-06-16T15:12:32.117061", "match_id": "8KzCrXRp", "match": {"event_id": "8KzCrXRp", "start_time": "2025-06-17T18:30:00", "sport_id": 1, "league_name": "BRAZIL: Serie B Superbet", "country_id": 39, "country_name": "", "home_team": {"name": "Volta Redonda", "short_name": "VOL", "image_url": ""}, "away_team": {"name": "<PERSON><PERSON>", "short_name": "AVA", "image_url": ""}, "status": "1", "home_score": "", "away_score": "", "current_result": "", "match_url": "https://www.flashscore.com/match/8KzCrXRp/#/match-summary", "league_index": 6, "tournament_hash": "vF2C38ii"}, "head_to_head": {"overall_home": [{"home_team": "<PERSON><PERSON>", "away_team": "Volta Redonda", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "America MG", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Remo", "away_team": "Volta Redonda", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Amazonas", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Cricium<PERSON>", "away_team": "Volta Redonda", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Paysandu PA", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Athletic Club", "away_team": "Volta Redonda", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "CRB", "away_team": "Volta Redonda", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Novorizontino", "away_team": "Volta Redonda", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "<PERSON><PERSON><PERSON>", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Fluminense", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Fluminense", "away_team": "Volta Redonda", "score": "4:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Volta Redonda", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Sampaio Correa FE", "away_team": "Volta Redonda", "score": "1:3", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Nova Iguacu", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Marica", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Vasco", "away_team": "Volta Redonda", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Portuguesa RJ", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Flamengo RJ", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "Botafogo RJ", "away_team": "Volta Redonda", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Volta Redonda", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Fluminense", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Volta Redonda", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Marica", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "Athletic Club", "away_team": "Volta Redonda", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Marica", "away_team": "Volta Redonda", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Athletic Club", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Serrano FC", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Sao Bernardo", "away_team": "Volta Redonda", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Botafogo PB", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Serrano FC", "away_team": "Volta Redonda", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Remo", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Remo", "away_team": "Volta Redonda", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Botafogo PB", "away_team": "Volta Redonda", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "America RJ", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Sao Bernardo", "score": "3:1", "result": "WIN", "date": ""}, {"home_team": "America RJ", "away_team": "Volta Redonda", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Figueirense", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Volta Redonda", "score": "5:1", "result": "LOST", "date": ""}, {"home_team": "Aparecidense", "away_team": "Volta Redonda", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Tombense", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Confianca", "away_team": "Volta Redonda", "score": "4:1", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Ypiranga FC", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Sao Bernardo", "score": "1:3", "result": "LOST", "date": ""}, {"home_team": "Ferroviario", "away_team": "Volta Redonda", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "<PERSON><PERSON><PERSON>", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "SER Caxias", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "ABC", "away_team": "Volta Redonda", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Athletic Club", "away_team": "Volta Redonda", "score": "3:2", "result": "LOST", "date": ""}], "overall_away": [{"home_team": "<PERSON><PERSON>", "away_team": "CRB", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Coritiba", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Chapecoense-SC", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "<PERSON><PERSON>", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Atletico GO", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "America MG", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Amazonas", "away_team": "<PERSON><PERSON>", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Operario-PR", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Novorizontino", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Chapecoense-SC", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Santa Catarina", "away_team": "<PERSON><PERSON>", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Figueirense", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Cricium<PERSON>", "score": "0:3", "result": "LOST", "date": ""}, {"home_team": "Barra FC", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Concordia", "score": "3:1", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "<PERSON><PERSON><PERSON>", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "0:3", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Joinville", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Figueirense", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Caravaggio", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Brusque", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Santa Catarina", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Ponte Preta", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Botafogo SP", "away_team": "<PERSON><PERSON>", "score": "1:3", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Mirassol", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Vila Nova FC", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Novorizontino", "away_team": "<PERSON><PERSON>", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Amazonas", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "America MG", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Brusque", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Guarani", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Ituano", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Sport Recife", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "CRB", "away_team": "<PERSON><PERSON>", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Coritiba", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Paysandu PA", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON>", "away_team": "<PERSON><PERSON>", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Operario-PR", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Ponte Preta", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Botafogo SP", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Mirassol", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Vila Nova FC", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}], "head_to_head": [{"home_team": "<PERSON><PERSON>", "away_team": "Volta Redonda", "score": "4:1", "result": "", "date": ""}, {"home_team": "Volta Redonda", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "", "date": ""}], "home_home": [{"home_team": "Volta Redonda", "away_team": "America MG", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Amazonas", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Paysandu PA", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "<PERSON><PERSON><PERSON>", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Fluminense", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Nova Iguacu", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Marica", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Portuguesa RJ", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Flamengo RJ", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Fluminense", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Marica", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Athletic Club", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Serrano FC", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Botafogo PB", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Remo", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "America RJ", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Sao Bernardo", "score": "3:1", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Figueirense", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Tombense", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Ypiranga FC", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Sao Bernardo", "score": "1:3", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "<PERSON><PERSON><PERSON>", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Volta Redonda", "away_team": "SER Caxias", "score": "1:0", "result": "WIN", "date": ""}], "away_away": [{"home_team": "Coritiba", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Ferroviaria", "away_team": "<PERSON><PERSON>", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Amazonas", "away_team": "<PERSON><PERSON>", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Santa Catarina", "away_team": "<PERSON><PERSON>", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Barra FC", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "0:3", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Caravaggio", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Brusque", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Botafogo SP", "away_team": "<PERSON><PERSON>", "score": "1:3", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Novorizontino", "away_team": "<PERSON><PERSON>", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Amazonas", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Guarani", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "CRB", "away_team": "<PERSON><PERSON>", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Coritiba", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON>", "away_team": "<PERSON><PERSON>", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Ponte Preta", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Mirassol", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Vila Nova FC", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "LOST", "date": ""}]}, "betting_odds": null}