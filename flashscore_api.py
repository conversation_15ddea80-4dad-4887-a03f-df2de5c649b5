"""
FlashScore API client
"""

import requests
import time
import logging
from datetime import datetime
from typing import Dict, Optional

logger = logging.getLogger(__name__)


class FlashScoreAPI:
    """FlashScore API client with proper headers and connection handling"""
    
    BASE_URL = "https://global.flashscore.ninja"
    BACKUP_URL = "https://local-global.flashscore.ninja"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(self._get_default_headers())
        self.max_retries = 3
        self.timeout = 7
        
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers for API requests"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/111.0',
            'Accept': '*/*',
            'Origin': 'https://www.flashscore.com',
            'Referer': 'https://www.flashscore.com/',
            'Accept-Language': 'ru-RU,ru;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'x-fsign': 'SW9D1eZo',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'X-Firefox-Spdy': 'h2',
            'x-geoip2-city-name': 'London',
            'x-geoip2-country-code': 'GB',
            'x-geoip2-iso-subdivision-code-0': 'GB-ENG',
            'x-geoip2-subdivision-code-0': 'ENG',
            'x-geoip2-subdivision-name-0': 'England',
            'x-geoip2-subdivision-name-1': '',
        }
    
    def make_request(self, url: str) -> Optional[requests.Response]:
        """Make HTTP request with retry logic"""
        for attempt in range(self.max_retries):
            try:
                response = self.session.get(url, timeout=self.timeout)
                if response.status_code == 200:
                    logger.debug(f"Successfully fetched: {url}")
                    return response
                else:
                    logger.warning(f"HTTP {response.status_code} for {url}")
                    time.sleep(0.1)
                    
            except requests.exceptions.RequestException as e:
                error_time = datetime.now().strftime("%d_%m_%Y %H:%M:%S")
                logger.error(f'[{error_time}]: Connection error: {e}\n{url}')
                time.sleep(0.1)
                
        logger.error(f"Failed to fetch after {self.max_retries} attempts: {url}")
        return None
    
    def get_matches_for_day(self, sport_id: int, day_offset: int = 0) -> Optional[str]:
        """
        Get matches for a specific sport and day
        
        Args:
            sport_id: Sport type ID (1=Football, 3=Basketball, etc.)
            day_offset: Days from today (-1=yesterday, 0=today, 1=tomorrow)
        
        Returns:
            Raw response text or None if failed
        """
        url = f'{self.BASE_URL}/2/x/feed/f_{sport_id}_{day_offset}_3_en_1'
        response = self.make_request(url)
        return response.text if response else None
    
    def get_head_to_head_data(self, match_id: str) -> Optional[str]:
        """
        Get head-to-head data for a specific match
        
        Args:
            match_id: Match ID from FlashScore
            
        Returns:
            Raw H2H data or None if failed
        """
        url = f'{self.BASE_URL}/2/x/feed/df_hh_1_{match_id}'
        response = self.make_request(url)
        return response.text if response else None
    
    def get_betting_odds(self, match_id: str) -> Optional[str]:
        """
        Get betting odds for a specific match
        
        Args:
            match_id: Match ID from FlashScore
            
        Returns:
            Raw odds data or None if failed
        """
        url = f'{self.BASE_URL}/2/x/feed/df_od_1_{match_id}_'
        response = self.make_request(url)
        return response.text if response else None
