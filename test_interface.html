<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlashScore Parser Test Interface</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        select, input, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            margin-left: 10px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .results {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .match-card {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fafafa;
        }
        
        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .teams {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .match-time {
            color: #666;
            font-size: 14px;
        }
        
        .league {
            color: #667eea;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.finished {
            background: #d4edda;
            color: #155724;
        }
        
        .status.live {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.scheduled {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        
        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .toggle-json {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>FlashScore Parser Test Interface</h1>
        <p>Test and visualize FlashScore API data extraction</p>
    </div>
    
    <div class="controls">
        <div class="control-group">
            <label for="sport-select">Sport:</label>
            <select id="sport-select">
                <option value="1">Football (Soccer)</option>
                <option value="3">Basketball</option>
                <option value="4">Hockey</option>
                <option value="6">Baseball</option>
                <option value="2">Tennis</option>
                <option value="25">Table Tennis</option>
            </select>
        </div>
        
        <div class="control-group">
            <label for="day-select">Day:</label>
            <select id="day-select">
                <option value="-1">Yesterday</option>
                <option value="0" selected>Today</option>
                <option value="1">Tomorrow</option>
                <option value="2">Day After Tomorrow</option>
            </select>
        </div>
        
        <div class="control-group">
            <button onclick="fetchMatches()">Fetch Matches</button>
            <button onclick="clearResults()">Clear Results</button>
            <button onclick="exportToJSON()">Export JSON</button>
        </div>
    </div>
    
    <div class="results" id="results">
        <div class="loading" id="welcome-message">
            <h3>Welcome to FlashScore Parser Test Interface</h3>
            <p>Select a sport and day, then click "Fetch Matches" to begin testing.</p>
            <p><strong>Recommended first test:</strong> Football + Today</p>
        </div>
    </div>

    <script>
        let currentData = null;
        
        function showLoading() {
            document.getElementById('results').innerHTML = `
                <div class="loading">
                    <h3>Loading matches...</h3>
                    <p>Please wait while we fetch data from FlashScore API</p>
                </div>
            `;
        }
        
        function showError(message) {
            document.getElementById('results').innerHTML = `
                <div class="error">
                    <h3>Error</h3>
                    <p>${message}</p>
                </div>
            `;
        }
        
        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.innerHTML = `<strong>Success:</strong> ${message}`;
            document.getElementById('results').insertBefore(successDiv, document.getElementById('results').firstChild);
            
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }
        
        function displayMatches(data) {
            if (!data || !data.matches || data.matches.length === 0) {
                document.getElementById('results').innerHTML = `
                    <div class="error">
                        <h3>No Matches Found</h3>
                        <p>No matches were found for the selected sport and day.</p>
                    </div>
                `;
                return;
            }
            
            currentData = data;
            
            // Calculate statistics
            const totalMatches = data.matches.length;
            const finishedMatches = data.matches.filter(m => m.status === 'FINISHED').length;
            const liveMatches = data.matches.filter(m => ['FIRST_HALF', 'SECOND_HALF', 'HALF_TIME'].includes(m.status)).length;
            const scheduledMatches = totalMatches - finishedMatches - liveMatches;
            
            let html = `
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number">${totalMatches}</div>
                        <div class="stat-label">Total Matches</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${finishedMatches}</div>
                        <div class="stat-label">Finished</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${liveMatches}</div>
                        <div class="stat-label">Live</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${scheduledMatches}</div>
                        <div class="stat-label">Scheduled</div>
                    </div>
                </div>
                
                <h3>Matches (${totalMatches} found)</h3>
            `;
            
            data.matches.forEach((match, index) => {
                const statusClass = getStatusClass(match.status);
                const matchTime = new Date(match.start_time).toLocaleString();
                
                html += `
                    <div class="match-card">
                        <div class="match-header">
                            <div class="teams">${match.home_team.name} vs ${match.away_team.name}</div>
                            <div class="status ${statusClass}">${match.status}</div>
                        </div>
                        <div class="league">${match.league_name}</div>
                        <div class="match-time">⏰ ${matchTime}</div>
                        ${match.current_result ? `<div><strong>Score:</strong> ${match.current_result}</div>` : ''}
                        <div><strong>Match ID:</strong> ${match.event_id}</div>
                        <button class="toggle-json" onclick="toggleMatchJSON(${index})">Show JSON</button>
                        <div id="json-${index}" class="json-viewer" style="display: none;">
                            <pre>${JSON.stringify(match, null, 2)}</pre>
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('results').innerHTML = html;
        }
        
        function getStatusClass(status) {
            if (status === 'FINISHED') return 'finished';
            if (['FIRST_HALF', 'SECOND_HALF', 'HALF_TIME'].includes(status)) return 'live';
            return 'scheduled';
        }
        
        function toggleMatchJSON(index) {
            const jsonDiv = document.getElementById(`json-${index}`);
            const button = jsonDiv.previousElementSibling;
            
            if (jsonDiv.style.display === 'none') {
                jsonDiv.style.display = 'block';
                button.textContent = 'Hide JSON';
            } else {
                jsonDiv.style.display = 'none';
                button.textContent = 'Show JSON';
            }
        }
        
        async function fetchMatches() {
            const sportId = document.getElementById('sport-select').value;
            const dayOffset = document.getElementById('day-select').value;
            
            showLoading();
            
            try {
                // This would normally call your Python parser
                // For now, we'll simulate the API call
                await simulateAPICall(sportId, dayOffset);
                
            } catch (error) {
                showError(`Failed to fetch matches: ${error.message}`);
            }
        }
        
        async function simulateAPICall(sportId, dayOffset) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Simulate sample data for demonstration
            const sampleData = {
                timestamp: new Date().toISOString(),
                total_matches: 3,
                matches: [
                    {
                        event_id: "sample123",
                        start_time: new Date().toISOString(),
                        sport_id: parseInt(sportId),
                        league_name: "Premier League",
                        country_id: 17,
                        country_name: "England",
                        home_team: {
                            name: "Arsenal",
                            short_name: "ARS",
                            image_url: ""
                        },
                        away_team: {
                            name: "Chelsea",
                            short_name: "CHE",
                            image_url: ""
                        },
                        status: "FINISHED",
                        home_score: "2",
                        away_score: "1",
                        current_result: "2:1",
                        match_url: "https://www.flashscore.com/match/sample123/"
                    },
                    {
                        event_id: "sample456",
                        start_time: new Date(Date.now() + 3600000).toISOString(),
                        sport_id: parseInt(sportId),
                        league_name: "La Liga",
                        country_id: 15,
                        country_name: "Spain",
                        home_team: {
                            name: "Barcelona",
                            short_name: "BAR",
                            image_url: ""
                        },
                        away_team: {
                            name: "Real Madrid",
                            short_name: "RMA",
                            image_url: ""
                        },
                        status: "SCHEDULED",
                        home_score: "",
                        away_score: "",
                        current_result: "",
                        match_url: "https://www.flashscore.com/match/sample456/"
                    },
                    {
                        event_id: "sample789",
                        start_time: new Date(Date.now() - 1800000).toISOString(),
                        sport_id: parseInt(sportId),
                        league_name: "Bundesliga",
                        country_id: 16,
                        country_name: "Germany",
                        home_team: {
                            name: "Bayern Munich",
                            short_name: "BAY",
                            image_url: ""
                        },
                        away_team: {
                            name: "Borussia Dortmund",
                            short_name: "BVB",
                            image_url: ""
                        },
                        status: "FIRST_HALF",
                        home_score: "1",
                        away_score: "0",
                        current_result: "1:0",
                        match_url: "https://www.flashscore.com/match/sample789/"
                    }
                ]
            };
            
            displayMatches(sampleData);
            showSuccess(`Loaded ${sampleData.matches.length} matches successfully!`);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = `
                <div class="loading" id="welcome-message">
                    <h3>Results Cleared</h3>
                    <p>Select a sport and day, then click "Fetch Matches" to begin testing.</p>
                </div>
            `;
            currentData = null;
        }
        
        function exportToJSON() {
            if (!currentData) {
                showError('No data to export. Please fetch matches first.');
                return;
            }
            
            const dataStr = JSON.stringify(currentData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `flashscore_matches_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            
            showSuccess('Data exported to JSON file successfully!');
        }
    </script>
</body>
</html>
