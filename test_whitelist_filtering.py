#!/usr/bin/env python3
"""
Test script for whitelist filtering functionality
Demonstrates the new league/championship filtering system
"""

import json
from datetime import datetime
from flashscore_parser import FlashScoreParser, SportType
from league_whitelist_config import (
    LeagueWhitelistManager, 
    WhitelistMode,
    get_whitelisted_country_ids,
    get_whitelisted_league_names
)


def print_separator(title: str):
    """Print a nice separator for test sections"""
    print("\n" + "="*70)
    print(f" {title}")
    print("="*70)


def test_whitelist_configuration():
    """Test 1: Verify whitelist configuration"""
    print_separator("TEST 1: Whitelist Configuration Verification")
    
    # Test default configuration
    manager = LeagueWhitelistManager()
    stats = manager.get_whitelist_stats(sport_id=1)
    
    print("Default Whitelist Configuration:")
    print(f"  Mode: {stats['mode']}")
    print(f"  Enabled: {stats['enabled']}")
    print(f"  Whitelisted Countries: {stats['whitelisted_countries']}")
    print(f"  Whitelisted Leagues: {stats['whitelisted_leagues']}")
    print(f"  Whitelisted Patterns: {stats['whitelisted_patterns']}")
    
    print(f"\nSample Whitelisted Countries: {stats['sample_countries']}")
    print(f"Sample Whitelisted Leagues: {stats['sample_leagues'][:5]}")
    print(f"Sample Patterns: {stats['sample_patterns'][:5]}")
    
    # Test different modes
    print(f"\nTesting Different Whitelist Modes:")
    for mode in WhitelistMode:
        manager.mode = mode
        print(f"  {mode.value}: {'✅ Active' if mode != WhitelistMode.DISABLED else '❌ Disabled'}")
    
    return True


def test_filtering_without_whitelist():
    """Test 2: Get matches without whitelist filtering"""
    print_separator("TEST 2: Matches Without Whitelist Filtering")
    
    parser = FlashScoreParser()
    
    # Disable whitelist to get all matches
    parser.enable_whitelist(False)
    
    matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=False)
    
    if matches:
        print(f"✅ Found {len(matches)} total matches (no filtering)")
        
        # Analyze leagues
        leagues = {}
        for match in matches:
            league_key = f"{match.league_name} (ID: {match.country_id})"
            if league_key not in leagues:
                leagues[league_key] = 0
            leagues[league_key] += 1
        
        print(f"Total unique leagues: {len(leagues)}")
        print(f"\nTop 10 leagues by match count:")
        sorted_leagues = sorted(leagues.items(), key=lambda x: x[1], reverse=True)
        for i, (league, count) in enumerate(sorted_leagues[:10]):
            print(f"  {i+1:2d}. {league}: {count} matches")
        
        # Save unfiltered data
        parser.save_matches_to_json(matches, "matches_unfiltered.json")
        print(f"\n📁 Unfiltered matches saved to: matches_unfiltered.json")
        
        return matches
    else:
        print("❌ No matches found")
        return []


def test_filtering_with_whitelist(unfiltered_matches):
    """Test 3: Apply whitelist filtering"""
    print_separator("TEST 3: Matches With Whitelist Filtering")
    
    parser = FlashScoreParser()
    
    # Enable whitelist filtering
    parser.enable_whitelist(True)
    
    matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=True)
    
    if matches:
        print(f"✅ Found {len(matches)} matches after whitelist filtering")
        
        if unfiltered_matches:
            filtered_count = len(unfiltered_matches) - len(matches)
            filter_percentage = (filtered_count / len(unfiltered_matches)) * 100
            print(f"📊 Filtering Statistics:")
            print(f"  Original matches: {len(unfiltered_matches)}")
            print(f"  Filtered matches: {len(matches)}")
            print(f"  Removed matches: {filtered_count}")
            print(f"  Filter rate: {filter_percentage:.1f}%")
        
        # Analyze filtered leagues
        leagues = {}
        for match in matches:
            league_key = f"{match.league_name} (ID: {match.country_id})"
            if league_key not in leagues:
                leagues[league_key] = 0
            leagues[league_key] += 1
        
        print(f"\nFiltered leagues: {len(leagues)}")
        print(f"Top 10 whitelisted leagues:")
        sorted_leagues = sorted(leagues.items(), key=lambda x: x[1], reverse=True)
        for i, (league, count) in enumerate(sorted_leagues[:10]):
            print(f"  {i+1:2d}. {league}: {count} matches")
        
        # Save filtered data
        parser.save_matches_to_json(matches, "matches_filtered.json")
        parser.save_matches_to_legacy_format(matches, "matches_legacy_format.json")
        print(f"\n📁 Filtered matches saved to: matches_filtered.json")
        print(f"📁 Legacy format saved to: matches_legacy_format.json")
        
        return matches
    else:
        print("❌ No matches found after filtering")
        return []


def test_different_whitelist_modes():
    """Test 4: Test different whitelist modes"""
    print_separator("TEST 4: Different Whitelist Modes")
    
    parser = FlashScoreParser()
    
    modes_to_test = [
        WhitelistMode.COUNTRY_ID,
        WhitelistMode.LEAGUE_NAME,
        WhitelistMode.COMBINED
    ]
    
    results = {}
    
    for mode in modes_to_test:
        print(f"\nTesting mode: {mode.value}")
        parser.update_whitelist_mode(mode)
        
        matches = parser.get_today_matches(SportType.FOOTBALL.value, apply_whitelist=True)
        
        results[mode.value] = {
            "match_count": len(matches),
            "leagues": len(set(match.league_name for match in matches))
        }
        
        print(f"  Matches found: {len(matches)}")
        print(f"  Unique leagues: {results[mode.value]['leagues']}")
    
    print(f"\n📊 Mode Comparison:")
    for mode, data in results.items():
        print(f"  {mode}: {data['match_count']} matches, {data['leagues']} leagues")
    
    # Save mode comparison
    with open("whitelist_mode_comparison.json", "w", encoding="utf-8") as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "mode_comparison": results,
            "test_sport": "Football"
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 Mode comparison saved to: whitelist_mode_comparison.json")


def test_league_filtering_analysis():
    """Test 5: Detailed league filtering analysis"""
    print_separator("TEST 5: Detailed League Filtering Analysis")
    
    parser = FlashScoreParser()
    
    # Get detailed filtering summary
    summary = parser.get_filtered_leagues_summary(SportType.FOOTBALL.value, day_offset=0)
    
    if "error" not in summary:
        print(f"📊 Filtering Analysis for Football:")
        print(f"  Total leagues found: {summary['total_leagues']}")
        print(f"  Whitelisted leagues: {summary['whitelisted_leagues']}")
        print(f"  Filtered out leagues: {summary['filtered_leagues']}")
        print(f"  Total matches: {summary['total_matches']}")
        print(f"  Whitelisted matches: {summary['whitelisted_matches']}")
        print(f"  Filtered out matches: {summary['filtered_matches']}")
        print(f"  Whitelist mode: {summary['whitelist_mode']}")
        
        # Show sample whitelisted leagues
        print(f"\nSample Whitelisted Leagues:")
        whitelisted = summary['leagues_detail']['whitelisted']
        for i, (league_key, info) in enumerate(list(whitelisted.items())[:10]):
            print(f"  {i+1:2d}. {info['league_name']} ({info['match_count']} matches)")
        
        # Show sample filtered leagues
        print(f"\nSample Filtered Out Leagues:")
        filtered = summary['leagues_detail']['filtered']
        for i, (league_key, info) in enumerate(list(filtered.items())[:10]):
            print(f"  {i+1:2d}. {info['league_name']} ({info['match_count']} matches)")
        
        # Save detailed analysis
        with open("league_filtering_analysis.json", "w", encoding="utf-8") as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 Detailed analysis saved to: league_filtering_analysis.json")
    else:
        print(f"❌ Error in analysis: {summary['error']}")


def test_multi_sport_filtering():
    """Test 6: Multi-sport whitelist filtering"""
    print_separator("TEST 6: Multi-Sport Whitelist Filtering")
    
    parser = FlashScoreParser()
    
    sports_to_test = [
        (SportType.FOOTBALL.value, "Football"),
        (SportType.BASKETBALL.value, "Basketball"),
        (SportType.TENNIS.value, "Tennis")
    ]
    
    multi_sport_results = {}
    
    for sport_id, sport_name in sports_to_test:
        print(f"\nTesting {sport_name} (ID: {sport_id})...")
        
        # Test without filtering
        parser.enable_whitelist(False)
        all_matches = parser.get_today_matches(sport_id, apply_whitelist=False)
        
        # Test with filtering
        parser.enable_whitelist(True)
        filtered_matches = parser.get_today_matches(sport_id, apply_whitelist=True)
        
        multi_sport_results[sport_name] = {
            "sport_id": sport_id,
            "total_matches": len(all_matches),
            "filtered_matches": len(filtered_matches),
            "filter_rate": ((len(all_matches) - len(filtered_matches)) / len(all_matches) * 100) if all_matches else 0
        }
        
        print(f"  Total matches: {len(all_matches)}")
        print(f"  Filtered matches: {len(filtered_matches)}")
        print(f"  Filter rate: {multi_sport_results[sport_name]['filter_rate']:.1f}%")
    
    # Save multi-sport results
    with open("multi_sport_filtering.json", "w", encoding="utf-8") as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "sports_tested": multi_sport_results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 Multi-sport results saved to: multi_sport_filtering.json")


def main():
    """Run all whitelist filtering tests"""
    print_separator("FlashScore Parser - Whitelist Filtering Tests")
    print("Testing the new league/championship filtering system...")
    
    try:
        # Test 1: Configuration verification
        test_whitelist_configuration()
        
        # Test 2: Get unfiltered matches
        unfiltered_matches = test_filtering_without_whitelist()
        
        # Test 3: Apply whitelist filtering
        filtered_matches = test_filtering_with_whitelist(unfiltered_matches)
        
        # Test 4: Different whitelist modes
        test_different_whitelist_modes()
        
        # Test 5: Detailed analysis
        test_league_filtering_analysis()
        
        # Test 6: Multi-sport filtering
        test_multi_sport_filtering()
        
        print_separator("WHITELIST FILTERING TESTS COMPLETED")
        print("✅ All whitelist filtering tests completed successfully!")
        print("\nGenerated files:")
        print("  - matches_unfiltered.json")
        print("  - matches_filtered.json")
        print("  - matches_legacy_format.json")
        print("  - whitelist_mode_comparison.json")
        print("  - league_filtering_analysis.json")
        print("  - multi_sport_filtering.json")
        print("\nKey Features Demonstrated:")
        print("  ✅ Configurable whitelist filtering")
        print("  ✅ Multiple filtering modes")
        print("  ✅ Legacy format compatibility")
        print("  ✅ Detailed filtering analysis")
        print("  ✅ Multi-sport support")
        print("  ✅ Easy enable/disable functionality")
        
    except Exception as e:
        print(f"❌ Testing failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
