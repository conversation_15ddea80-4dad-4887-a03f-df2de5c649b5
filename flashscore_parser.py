"""
Modern FlashScore Parser
A refactored and improved version of the original parser with better structure,
error handling, and API integration capabilities.
"""

import requests
import time
import json
import logging
import os
from datetime import timedel<PERSON>, datetime, date
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

# Import whitelist configuration
from league_whitelist_config import (
    LeagueWhitelistManager,
    WhitelistMode,
    default_whitelist_manager,
    is_league_whitelisted
)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('flashscore_parser.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SportType(Enum):
    """Supported sport types with their IDs"""
    FOOTBALL = 1
    TENNIS = 2
    BASKETBALL = 3
    HOCKEY = 4
    AMERICAN_FOOTBALL = 5
    BASEBALL = 6
    HANDBALL = 7
    VOLLEYBALL = 12
    BADMINTON = 21
    TABLE_TENNIS = 25


class MatchStatus(Enum):
    """Match status codes and their meanings"""
    FINISHED = '3'
    CANCELLED = '5'
    POSTPONED = '4'
    AFTER_PENALTIES = '11'
    AFTER_EXTRA_TIME = '10'
    TECHNICAL_DEFEAT = '54'
    NO_SHOW = '9'
    FIRST_HALF = '12'
    SECOND_HALF = '13'
    HALF_TIME = '38'
    AWAITING_UPDATES = '42'


@dataclass
class Team:
    """Team information"""
    name: str
    short_name: str
    image_url: str = ""


@dataclass
class Match:
    """Match information structure"""
    event_id: str
    start_time: datetime
    sport_id: int
    league_name: str
    country_id: int
    country_name: str
    home_team: Team
    away_team: Team
    status: str
    home_score: str = ""
    away_score: str = ""
    current_result: str = ""
    match_url: str = ""
    league_index: int = -1  # Position of league in API response (for whitelist filtering)
    tournament_hash: str = ""  # Tournament hash from ZC field (permanent ID)


@dataclass
class BettingOdds:
    """Betting odds structure"""
    home_win: float = 0.0
    draw: float = 0.0
    away_win: float = 0.0
    over_under_line: str = ""
    over_odds: float = 0.0
    under_odds: float = 0.0
    both_teams_score_yes: float = 0.0
    both_teams_score_no: float = 0.0


@dataclass
class HeadToHeadMatch:
    """Single head-to-head match data"""
    home_team: str
    away_team: str
    score: str
    result: str
    date: str = ""


@dataclass
class HeadToHeadData:
    """Complete head-to-head statistics"""
    overall_home: List[HeadToHeadMatch]
    overall_away: List[HeadToHeadMatch]
    head_to_head: List[HeadToHeadMatch]
    home_home: List[HeadToHeadMatch]
    away_away: List[HeadToHeadMatch]


@dataclass
class MatchStatistics:
    """Complete match statistics including H2H data"""
    match: Match
    head_to_head: Optional[HeadToHeadData] = None
    betting_odds: Optional[BettingOdds] = None


class FlashScoreAPI:
    """FlashScore API client with proper headers and connection handling"""
    
    BASE_URL = "https://global.flashscore.ninja"
    BACKUP_URL = "https://local-global.flashscore.ninja"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(self._get_default_headers())
        self.max_retries = 3
        self.timeout = 7
        
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers for API requests"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/111.0',
            'Accept': '*/*',
            'Origin': 'https://www.flashscore.com',
            'Referer': 'https://www.flashscore.com/',
            'Accept-Language': 'ru-RU,ru;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'x-fsign': 'SW9D1eZo',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',
            'X-Firefox-Spdy': 'h2',
            'x-geoip2-city-name': 'London',
            'x-geoip2-country-code': 'GB',
            'x-geoip2-iso-subdivision-code-0': 'GB-ENG',
            'x-geoip2-subdivision-code-0': 'ENG',
            'x-geoip2-subdivision-name-0': 'England',
            'x-geoip2-subdivision-name-1': '',
        }
    
    def make_request(self, url: str) -> Optional[requests.Response]:
        """Make HTTP request with retry logic"""
        for attempt in range(self.max_retries):
            try:
                response = self.session.get(url, timeout=self.timeout)
                if response.status_code == 200:
                    logger.debug(f"Successfully fetched: {url}")
                    return response
                else:
                    logger.warning(f"HTTP {response.status_code} for {url}")
                    time.sleep(0.1)
                    
            except requests.exceptions.RequestException as e:
                error_time = datetime.now().strftime("%d_%m_%Y %H:%M:%S")
                logger.error(f'[{error_time}]: Connection error: {e}\n{url}')
                time.sleep(0.1)
                
        logger.error(f"Failed to fetch after {self.max_retries} attempts: {url}")
        return None
    
    def get_matches_for_day(self, sport_id: int, day_offset: int = 0) -> Optional[str]:
        """
        Get matches for a specific sport and day
        
        Args:
            sport_id: Sport type ID (1=Football, 3=Basketball, etc.)
            day_offset: Days from today (-1=yesterday, 0=today, 1=tomorrow)
        
        Returns:
            Raw response text or None if failed
        """
        url = f'{self.BASE_URL}/2/x/feed/f_{sport_id}_{day_offset}_3_en_1'
        response = self.make_request(url)
        return response.text if response else None
    
    def get_head_to_head_data(self, match_id: str) -> Optional[str]:
        """
        Get head-to-head data for a specific match
        
        Args:
            match_id: Match ID from FlashScore
            
        Returns:
            Raw H2H data or None if failed
        """
        url = f'{self.BASE_URL}/2/x/feed/df_hh_1_{match_id}'
        response = self.make_request(url)
        return response.text if response else None
    
    def get_betting_odds(self, match_id: str) -> Optional[str]:
        """
        Get betting odds for a specific match
        
        Args:
            match_id: Match ID from FlashScore
            
        Returns:
            Raw odds data or None if failed
        """
        url = f'{self.BASE_URL}/2/x/feed/df_od_1_{match_id}_'
        response = self.make_request(url)
        return response.text if response else None


class MatchParser:
    """Parser for match data from FlashScore API responses"""
    
    def __init__(self):
        self.status_mapping = {
            '3': 'FINISHED',
            '5': 'CANCELLED', 
            '4': 'POSTPONED',
            '11': 'AFTER_PENALTIES',
            '10': 'AFTER_EXTRA_TIME',
            '54': 'TECHNICAL_DEFEAT',
            '9': 'NO_SHOW'
        }
    
    def parse_matches_from_response(self, response_text: str, sport_id: int) -> List[Match]:
        """
        Parse matches from API response text
        
        Args:
            response_text: Raw API response
            sport_id: Sport type ID
            
        Returns:
            List of parsed Match objects
        """
        matches = []
        
        try:
            # Split response by league sections
            league_sections = response_text.split('~ZA÷')

            for league_index, section in enumerate(league_sections[1:]):  # Skip first empty section
                try:
                    # Extract league info
                    country_id = int(section.split('ZB÷')[1].split('¬')[0])
                    league_name = section.split('¬')[0]

                    # Extract tournament hash (ZC field)
                    tournament_hash = ""
                    lines = section.split('¬')
                    for line in lines:
                        if line.startswith('ZC÷'):
                            tournament_hash = line[3:]
                            break

                    # Extract individual matches
                    match_sections = section.split('~AA÷')

                    for match_section in match_sections[1:]:  # Skip first section
                        match = self._parse_single_match(match_section, sport_id, league_name, country_id, league_index, tournament_hash)
                        if match:
                            matches.append(match)

                except Exception as e:
                    logger.warning(f"Error parsing league section: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error parsing matches response: {e}")
            
        return matches
    
    def _parse_single_match(self, match_text: str, sport_id: int, league_name: str, country_id: int, league_index: int, tournament_hash: str) -> Optional[Match]:
        """Parse a single match from text data"""
        try:
            # Extract basic match info
            event_id = match_text.split('¬')[0]
            status = match_text.split('AC÷')[1].split('¬')[0]
            
            # Parse timestamp
            timestamp = int(match_text.split('AD÷')[1].split('¬')[0])
            start_time = datetime.fromtimestamp(timestamp)
            
            # Extract team names
            home_team_name = match_text.split('AE÷')[1].split('¬')[0]
            away_team_name = match_text.split('AF÷')[1].split('¬')[0]
            
            # Extract short names
            home_short = match_text.split('WM÷')[1].split('¬')[0]
            away_short = match_text.split('WN÷')[1].split('¬')[0]
            
            # Extract scores (if available)
            home_score = ""
            away_score = ""
            try:
                home_score = match_text.split('AG÷')[1].split('¬')[0]
                away_score = match_text.split('AH÷')[1].split('¬')[0]
            except:
                pass
            
            # Create team objects
            home_team = Team(name=home_team_name, short_name=home_short)
            away_team = Team(name=away_team_name, short_name=away_short)
            
            # Create match URL
            match_url = f'https://www.flashscore.com/match/{event_id}/#/match-summary'
            
            # Create match object
            match = Match(
                event_id=event_id,
                start_time=start_time,
                sport_id=sport_id,
                league_name=league_name,
                country_id=country_id,
                country_name="",  # Will be filled later if needed
                home_team=home_team,
                away_team=away_team,
                status=self.status_mapping.get(status, status),
                home_score=home_score,
                away_score=away_score,
                current_result=f"{home_score}:{away_score}" if home_score and away_score else "",
                match_url=match_url,
                league_index=league_index,  # Position of league in API response
                tournament_hash=tournament_hash  # Tournament hash from ZC field
            )
            
            return match
            
        except Exception as e:
            logger.warning(f"Error parsing single match: {e}")
            return None


class HeadToHeadParser:
    """Parser for head-to-head statistics from FlashScore API"""

    def __init__(self):
        self.status_mapping = {
            '3': 'FINISHED',
            '5': 'CANCELLED',
            '4': 'POSTPONED',
            '11': 'AFTER_PENALTIES',
            '10': 'AFTER_EXTRA_TIME',
            '54': 'TECHNICAL_DEFEAT',
            '9': 'NO_SHOW'
        }
        self.result_mapping = {
            'l': 'LOST',
            'w': 'WIN',
            'd': 'DRAW',
            'lo': 'LOST',
            'wo': 'WIN'
        }

    def parse_h2h_data(self, response_text: str) -> Optional[HeadToHeadData]:
        """
        Parse head-to-head data from API response

        Args:
            response_text: Raw H2H API response

        Returns:
            HeadToHeadData object or None if parsing failed
        """
        try:
            if not response_text:
                return None

            # Split response by sections
            sections = response_text.split('~KB÷')
            if len(sections) < 7:
                logger.warning("Insufficient H2H data sections")
                return None

            # Parse different sections
            overall_home = self._parse_h2h_section(sections[1])
            overall_away = self._parse_h2h_section(sections[2])
            head_to_head = self._parse_h2h_section(sections[3])
            home_home = self._parse_h2h_section(sections[4]) if len(sections) > 4 else []
            away_away = self._parse_h2h_section(sections[6]) if len(sections) > 6 else []

            return HeadToHeadData(
                overall_home=overall_home,
                overall_away=overall_away,
                head_to_head=head_to_head,
                home_home=home_home,
                away_away=away_away
            )

        except Exception as e:
            logger.error(f"Error parsing H2H data: {e}")
            return None

    def _parse_h2h_section(self, section_text: str) -> List[HeadToHeadMatch]:
        """Parse individual H2H section"""
        matches = []

        try:
            # Split by match entries
            match_entries = section_text.split('~')

            for entry in match_entries:
                if 'KJ÷' in entry and 'KK÷' in entry:
                    try:
                        # Extract team names
                        home_team = entry.split('KJ÷')[1].split('¬')[0].replace('*', '').strip().split('(')[0].strip()
                        away_team = entry.split('KK÷')[1].split('¬')[0].replace('*', '').strip().split('(')[0].strip()

                        # Extract score
                        score = ""
                        if 'KL÷' in entry:
                            score = entry.split('KL÷')[1].split('¬')[0].strip()
                        elif 'KM÷' in entry:
                            try:
                                score = entry.split('KM÷')[1].split('¬')[0].split('(')[1].split(')')[0].strip()
                            except:
                                score = entry.split('KM÷')[1].split('¬')[0].strip()

                        # Extract result
                        result = ""
                        if 'KN÷' in entry:
                            result = entry.split('KN÷')[1].split('¬')[0].strip()
                            result = self.result_mapping.get(result, result)

                        if home_team and away_team:
                            matches.append(HeadToHeadMatch(
                                home_team=home_team,
                                away_team=away_team,
                                score=score,
                                result=result
                            ))

                    except Exception as e:
                        logger.debug(f"Error parsing H2H match entry: {e}")
                        continue

        except Exception as e:
            logger.warning(f"Error parsing H2H section: {e}")

        return matches


class FlashScoreParser:
    """Main parser class that coordinates API calls and data processing"""

    def __init__(self, whitelist_manager: LeagueWhitelistManager = None):
        self.api = FlashScoreAPI()
        self.match_parser = MatchParser()
        self.h2h_parser = HeadToHeadParser()
        self.whitelist_manager = whitelist_manager or default_whitelist_manager

    def get_today_matches(self, sport_id: int, apply_whitelist: bool = True) -> List[Match]:
        """Get all matches for today for a specific sport"""
        logger.info(f"Fetching today's matches for sport {sport_id}")

        response_text = self.api.get_matches_for_day(sport_id, day_offset=0)
        if not response_text:
            logger.error("Failed to fetch today's matches")
            return []

        matches = self.match_parser.parse_matches_from_response(response_text, sport_id)
        logger.info(f"Found {len(matches)} matches before filtering")

        if apply_whitelist:
            matches = self._apply_whitelist_filter(matches, sport_id)
            logger.info(f"Found {len(matches)} matches after whitelist filtering")
        else:
            logger.info(f"Found {len(matches)} matches (whitelist disabled)")

        return matches

    def get_matches_for_date_range(self, sport_id: int, days: List[int], apply_whitelist: bool = True) -> List[Match]:
        """Get matches for multiple days"""
        all_matches = []

        for day_offset in days:
            logger.info(f"Fetching matches for day offset {day_offset}")
            response_text = self.api.get_matches_for_day(sport_id, day_offset)

            if response_text:
                matches = self.match_parser.parse_matches_from_response(response_text, sport_id)
                all_matches.extend(matches)
                logger.info(f"Found {len(matches)} matches for day {day_offset}")
            else:
                logger.warning(f"No data for day offset {day_offset}")

        if apply_whitelist and all_matches:
            original_count = len(all_matches)
            all_matches = self._apply_whitelist_filter(all_matches, sport_id)
            logger.info(f"Filtered {original_count} matches to {len(all_matches)} using whitelist")

        return all_matches

    def get_leagues_for_today(self, sport_id: int) -> Dict[str, List[str]]:
        """Get available leagues for today for a specific sport"""
        logger.info(f"Fetching available leagues for sport {sport_id}")

        response_text = self.api.get_matches_for_day(sport_id, day_offset=0)
        if not response_text:
            logger.error("Failed to fetch today's data")
            return {}

        leagues = {}
        try:
            # Split response by league sections
            league_sections = response_text.split('~ZA÷')

            for section in league_sections[1:]:  # Skip first empty section
                try:
                    # Extract league info
                    country_id = int(section.split('ZB÷')[1].split('¬')[0])
                    league_name = section.split('¬')[0]

                    # Count matches in this league
                    match_sections = section.split('~AA÷')
                    match_count = len(match_sections) - 1  # Subtract 1 for the first empty section

                    if league_name not in leagues:
                        leagues[league_name] = []

                    leagues[league_name].append(f"Country ID: {country_id}, Matches: {match_count}")

                except Exception as e:
                    logger.warning(f"Error parsing league section: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error parsing leagues response: {e}")

        logger.info(f"Found {len(leagues)} leagues")
        return leagues

    def _apply_whitelist_filter(self, matches: List[Match], sport_id: int) -> List[Match]:
        """
        Apply whitelist filtering to matches based on league/country criteria

        Args:
            matches: List of matches to filter
            sport_id: Sport ID for sport-specific filtering

        Returns:
            Filtered list of matches
        """
        if not self.whitelist_manager.enabled:
            logger.info("Whitelist filtering is disabled")
            return matches

        filtered_matches = []
        filtered_count = 0

        for match in matches:
            # Check if league is whitelisted using tournament_hash for TOURNAMENT_ID mode
            if (self.whitelist_manager.mode == WhitelistMode.TOURNAMENT_ID and
                hasattr(match, 'tournament_hash') and match.tournament_hash):
                # Use tournament hash for filtering (permanent ID)
                from whitelist import sp_good_ligs
                if match.tournament_hash in sp_good_ligs:
                    filtered_matches.append(match)
                else:
                    filtered_count += 1
                    logger.debug(f"Filtered out: {match.league_name} (Hash: {match.tournament_hash})")
            elif self.whitelist_manager.is_league_whitelisted(
                country_id=match.country_id,
                league_name=match.league_name,
                sport_id=sport_id
            ):
                filtered_matches.append(match)
            else:
                filtered_count += 1
                logger.debug(f"Filtered out: {match.league_name} (Country ID: {match.country_id})")

        logger.info(f"Whitelist filtering: kept {len(filtered_matches)} matches, filtered out {filtered_count}")
        return filtered_matches

    def get_whitelist_stats(self, sport_id: int = 1) -> Dict:
        """Get statistics about current whitelist configuration"""
        return self.whitelist_manager.get_whitelist_stats(sport_id)

    def update_whitelist_mode(self, mode: WhitelistMode) -> None:
        """Update whitelist filtering mode"""
        self.whitelist_manager.mode = mode
        logger.info(f"Updated whitelist mode to: {mode.value}")

    def enable_whitelist(self, enabled: bool = True) -> None:
        """Enable or disable whitelist filtering"""
        self.whitelist_manager.enabled = enabled
        logger.info(f"Whitelist filtering {'enabled' if enabled else 'disabled'}")

    def get_filtered_leagues_summary(self, sport_id: int, day_offset: int = 0) -> Dict:
        """
        Get summary of leagues before and after filtering

        Args:
            sport_id: Sport ID to analyze
            day_offset: Day offset (0=today, 1=tomorrow, etc.)

        Returns:
            Dictionary with filtering statistics
        """
        # Get all leagues without filtering
        response_text = self.api.get_matches_for_day(sport_id, day_offset)
        if not response_text:
            return {"error": "Failed to fetch data"}

        all_matches = self.match_parser.parse_matches_from_response(response_text, sport_id)

        # Analyze all leagues
        all_leagues = {}
        for match in all_matches:
            league_key = f"{match.league_name} (ID: {match.country_id})"
            if league_key not in all_leagues:
                all_leagues[league_key] = {
                    "league_name": match.league_name,
                    "country_id": match.country_id,
                    "match_count": 0,
                    "whitelisted": False
                }
            all_leagues[league_key]["match_count"] += 1

        # Check whitelist status for each league
        whitelisted_leagues = {}
        filtered_leagues = {}

        for league_key, league_info in all_leagues.items():
            is_whitelisted = self.whitelist_manager.is_league_whitelisted(
                country_id=league_info["country_id"],
                league_name=league_info["league_name"],
                sport_id=sport_id
            )

            league_info["whitelisted"] = is_whitelisted

            if is_whitelisted:
                whitelisted_leagues[league_key] = league_info
            else:
                filtered_leagues[league_key] = league_info

        return {
            "sport_id": sport_id,
            "day_offset": day_offset,
            "total_leagues": len(all_leagues),
            "whitelisted_leagues": len(whitelisted_leagues),
            "filtered_leagues": len(filtered_leagues),
            "total_matches": len(all_matches),
            "whitelisted_matches": sum(l["match_count"] for l in whitelisted_leagues.values()),
            "filtered_matches": sum(l["match_count"] for l in filtered_leagues.values()),
            "whitelist_enabled": self.whitelist_manager.enabled,
            "whitelist_mode": self.whitelist_manager.mode.value,
            "leagues_detail": {
                "whitelisted": whitelisted_leagues,
                "filtered": filtered_leagues
            }
        }
    
    def save_matches_to_json(self, matches: List[Match], filename: str,
                           include_whitelist_info: bool = True) -> bool:
        """Save matches to JSON file with optional whitelist information"""
        try:
            # Convert matches to dictionaries for JSON serialization
            matches_data = []
            for match in matches:
                match_dict = asdict(match)
                # Convert datetime to string for JSON serialization
                match_dict['start_time'] = match.start_time.isoformat()
                matches_data.append(match_dict)

            # Create output structure with whitelist information
            output_data = {
                "timestamp": datetime.now().isoformat(),
                "total_matches": len(matches),
                "matches": matches_data
            }

            # Add whitelist information if requested
            if include_whitelist_info:
                output_data["whitelist_info"] = {
                    "enabled": self.whitelist_manager.enabled,
                    "mode": self.whitelist_manager.mode.value,
                    "filtering_applied": self.whitelist_manager.enabled,
                    "stats": self.whitelist_manager.get_whitelist_stats(
                        matches[0].sport_id if matches else 1
                    )
                }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(matches)} matches to {filename}")
            return True

        except Exception as e:
            logger.error(f"Error saving matches to JSON: {e}")
            return False

    def save_matches_to_legacy_format(self, matches: List[Match], filename: str) -> bool:
        """
        Save matches in the original parser's JSON format for backward compatibility

        This method converts the modern Match objects back to the original format
        used by the legacy parser for maximum compatibility.
        """
        try:
            legacy_matches = []

            for match in matches:
                # Convert to original format:
                # [data_matcha, [liga,int(country_id)], team1, team2, '','','',sc1,sc2,'',status, url_game,'',
                #  short_team1, short_team2, '', '', '','','','', sport_id, id_game]

                legacy_match = [
                    match.start_time.strftime('%d.%m.%Y %H:%M'),  # data_matcha
                    [match.league_name, match.country_id],        # [liga, country_id]
                    match.home_team.name,                         # team1
                    match.away_team.name,                         # team2
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    match.home_score,                             # sc1
                    match.away_score,                             # sc2
                    '',                                           # placeholder
                    match.status,                                 # status
                    match.match_url,                              # url_game
                    '',                                           # placeholder
                    match.home_team.short_name,                   # short_team1
                    match.away_team.short_name,                   # short_team2
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    match.sport_id,                               # sport_id
                    match.event_id                                # id_game
                ]

                legacy_matches.append(legacy_match)

            # Create legacy output structure
            legacy_output = {
                "timestamp": datetime.now().isoformat(),
                "total_matches": len(legacy_matches),
                "format": "legacy_compatible",
                "matches": legacy_matches,
                "whitelist_applied": self.whitelist_manager.enabled
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(legacy_output, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(legacy_matches)} matches to {filename} in legacy format")
            return True

        except Exception as e:
            logger.error(f"Error saving matches to legacy JSON format: {e}")
            return False

    def get_upcoming_matches(self, sport_id: int = 1, days_ahead: int = 7, apply_whitelist: bool = True) -> List[Match]:
        """
        Get upcoming matches for specified number of days ahead

        Args:
            sport_id: Sport ID (1=Football)
            days_ahead: Number of days to look ahead (default: 7)
            apply_whitelist: Whether to apply whitelist filtering

        Returns:
            List of upcoming matches
        """
        logger.info(f"Fetching upcoming matches for sport {sport_id}, {days_ahead} days ahead")

        # Get matches for multiple days (1 to days_ahead)
        days_range = list(range(1, days_ahead + 1))
        upcoming_matches = self.get_matches_for_date_range(sport_id, days_range, apply_whitelist)

        # Filter only upcoming matches (not finished)
        upcoming_only = []
        for match in upcoming_matches:
            if match.status not in ['FINISHED', 'CANCELLED']:
                upcoming_only.append(match)

        logger.info(f"Found {len(upcoming_only)} upcoming matches")
        return upcoming_only

    def get_match_statistics(self, match: Match) -> Optional[MatchStatistics]:
        """
        Get complete statistics for a match including H2H data

        Args:
            match: Match object

        Returns:
            MatchStatistics object with H2H data or None if failed
        """
        logger.info(f"Fetching statistics for match {match.event_id}: {match.home_team.name} vs {match.away_team.name}")

        try:
            # Get H2H data
            h2h_response = self.api.get_head_to_head_data(match.event_id)
            h2h_data = None

            if h2h_response:
                h2h_data = self.h2h_parser.parse_h2h_data(h2h_response)
                if h2h_data:
                    logger.info(f"Successfully parsed H2H data for match {match.event_id}")
                else:
                    logger.warning(f"Failed to parse H2H data for match {match.event_id}")
            else:
                logger.warning(f"No H2H data available for match {match.event_id}")

            return MatchStatistics(
                match=match,
                head_to_head=h2h_data,
                betting_odds=None  # Can be extended later
            )

        except Exception as e:
            logger.error(f"Error getting statistics for match {match.event_id}: {e}")
            return None

    def save_match_with_statistics(self, match_stats: MatchStatistics, output_dir: str = "matches") -> bool:
        """
        Save individual match with its statistics to JSON file

        Args:
            match_stats: MatchStatistics object
            output_dir: Directory to save files

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Create filename based on match ID
            filename = f"{match_stats.match.event_id}.json"
            filepath = os.path.join(output_dir, filename)

            # Prepare data for JSON serialization
            match_dict = asdict(match_stats.match)
            match_dict['start_time'] = match_stats.match.start_time.isoformat()

            output_data = {
                "timestamp": datetime.now().isoformat(),
                "match_id": match_stats.match.event_id,
                "match": match_dict,
                "head_to_head": asdict(match_stats.head_to_head) if match_stats.head_to_head else None,
                "betting_odds": asdict(match_stats.betting_odds) if match_stats.betting_odds else None
            }

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved match statistics to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving match statistics: {e}")
            return False

    def save_match_in_ireland_format(self, match_stats: MatchStatistics, filepath: str) -> bool:
        """
        Save match in Ireland Premier Division format using existing parser.py functions

        Args:
            match_stats: MatchStatistics object
            filepath: Full path to save the file

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            match = match_stats.match

            # Create match_info structure
            match_info = {
                "event_id": match.event_id,
                "home_team": match.home_team.name,
                "away_team": match.away_team.name,
                "short_home": match.home_team.short_name,
                "short_away": match.away_team.short_name,
                "start_time": match.start_time.isoformat(),
                "league": match.league_name,
                "tournament_hash": match.tournament_hash,
                "current_result": match.current_result,
                "status": match.status,
                "match_url": match.match_url
            }

            # Get H2H data using existing parser.py function
            h2h_data = self._get_h2h_data_from_api(match.event_id, match.home_team.short_name, match.away_team.short_name)

            # Create the main structure with event_id as key
            output_data = {
                match.event_id: {
                    "match_info": match_info,
                    "h2h_data": h2h_data
                }
            }

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved match in Ireland format to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving match in Ireland format: {e}")
            return False

    def _get_h2h_data_from_api(self, event_id: str, short_home: str, short_away: str) -> dict:
        """
        Get H2H data from API using existing parser.py functions

        Args:
            event_id: Match event ID
            short_home: Home team short name
            short_away: Away team short name

        Returns:
            H2H data in Ireland format
        """
        try:
            import requests

            # Use the same URL pattern as in parser.py
            url = f'https://global.flashscore.ninja/2/x/feed/df_hh_1_{event_id}'

            response = requests.get(url, timeout=10)
            if response.status_code == 200 and response.text:
                all_dannie_text = response.text
                t_names = all_dannie_text.split('~KA÷')
                dannie_text_all = all_dannie_text.split('~KB÷')

                if len(dannie_text_all) >= 7:
                    # Import functions from parser.py
                    from parser import poisk_h2h_json, poisk_h2h_json_lichki

                    dannie_overall_home = poisk_h2h_json(dannie_text_all[1])
                    dannie_overall_away = poisk_h2h_json(dannie_text_all[2])
                    dannie_overall_lichki = poisk_h2h_json_lichki(dannie_text_all[3])

                    overall_json = {
                        "TAB_NAME": t_names[1].split('¬')[0] if len(t_names) > 1 else "Overall",
                        "GROUPS": [dannie_overall_home, dannie_overall_away, dannie_overall_lichki]
                    }

                    h2h_json = {"DATA": [overall_json]}

                    # Add home and away specific data
                    if len(dannie_text_all) >= 7:
                        dannie_home_home = self._poisk_h2h_json(dannie_text_all[4])
                        dannie_home_lichki = self._poisk_h2h_json_lichki(dannie_text_all[5])
                        home_home_json = {
                            "TAB_NAME": f"{short_home} - Home",
                            "GROUPS": [dannie_home_home, dannie_home_lichki]
                        }
                        h2h_json["DATA"].append(home_home_json)

                        dannie_away_away = self._poisk_h2h_json(dannie_text_all[6])
                        away_away_json = {
                            "TAB_NAME": f"{short_away} - Away",
                            "GROUPS": [dannie_away_away, dannie_home_lichki]
                        }
                        h2h_json["DATA"].append(away_away_json)

                    return h2h_json

        except Exception as e:
            logger.warning(f"Error getting H2H data from API: {e}")

        return {"DATA": []}

    def _poisk_h2h_json(self, dannie_text: str) -> dict:
        """
        Parse H2H JSON data (copied from parser.py poisk_h2h_json function)
        """
        dannie_matchs = {"GROUP_LABEL": "", "ITEMS": []}
        stage = {'3': 'FINISHED', '5': 'CANCELLED', '4': 'Postponed', '11': 'After Penalties',
                '10': 'After extra time', '54': 'Those. defeat', '9': 'No-show'}
        result = {"l": "LOST", "w": "WIN", "d": "DRAW", "lo": "LOST", "wo": "WIN"}

        try:
            h2hs_homes = dannie_text.split('¬~')
            dannie_matchs["GROUP_LABEL"] = h2hs_homes[0]

            for h2h in h2hs_homes[1:]:
                match_item = {
                    "START_TIME": "", "EVENT_ID": "", "EVENT_NAME": "", "STAGE": "", "COUNTRY_ID": "", "COUNTRY_NAME": "",
                    "EVENT_ACRONYM": "", "HOME_PARTICIPANT": "", "HOME_PARTICIPANT_NAME_ONE": "", "HOME_PARTICIPANT_NAME_TWO": None,
                    "AWAY_PARTICIPANT": "", "AWAY_PARTICIPANT_NAME_ONE": "", "AWAY_PARTICIPANT_NAME_TWO": None, "CURRENT_RESULT": "",
                    "HOME_SCORE_FULL": "", "AWAY_SCORE_FULL": "", "HOME_IMAGES": [""], "AWAY_IMAGES": [""], "H_RESULT": "", "TEAM_MARK": ""
                }
                try:
                    match_item["EVENT_ID"] = h2h.split('KP÷')[1].split('¬')[0]
                    match_item["START_TIME"] = int(h2h.split('KC÷')[1].split('¬')[0])
                    match_item["EVENT_NAME"] = h2h.split('KF÷')[1].split('¬')[0]
                    try:
                        match_item["STAGE"] = stage[h2h.split('AC÷')[1].split('¬')[0]]
                    except:
                        pass

                    match_item["COUNTRY_ID"] = int(h2h.split('KG÷')[1].split('¬')[0])
                    match_item["COUNTRY_NAME"] = h2h.split('KH÷')[1].split('¬')[0]
                    match_item["EVENT_ACRONYM"] = h2h.split('KI÷')[1].split('¬')[0]
                    match_item["HOME_PARTICIPANT"] = h2h.split('KJ÷')[1].split('¬')[0]
                    match_item["HOME_PARTICIPANT_NAME_ONE"] = h2h.split('FH÷')[1].split('¬')[0]
                    match_item["AWAY_PARTICIPANT"] = h2h.split('KK÷')[1].split('¬')[0]
                    match_item["AWAY_PARTICIPANT_NAME_ONE"] = h2h.split('FK÷')[1].split('¬')[0]
                    match_item["CURRENT_RESULT"] = h2h.split('KL÷')[1].split('¬')[0]
                    match_item["HOME_SCORE_FULL"] = h2h.split('KU÷')[1].split('¬')[0]
                    match_item["AWAY_SCORE_FULL"] = h2h.split('KT÷')[1].split('¬')[0]
                    match_item["HOME_IMAGES"][0] = f"https://www.flashscore.com/res/image/data/{h2h.split('EC÷')[1].split('¬')[0]}"
                    match_item["AWAY_IMAGES"][0] = f"https://www.flashscore.com/res/image/data/{h2h.split('ED÷')[1].split('¬')[0]}"
                    match_item["H_RESULT"] = result[h2h.split('KN÷')[1].split('¬')[0]]
                    match_item["TEAM_MARK"] = h2h.split('KS÷')[1].split('¬')[0]

                    dannie_matchs["ITEMS"].append(match_item)
                except:
                    pass
        except:
            pass

        return dannie_matchs

    def _poisk_h2h_json_lichki(self, dannie_text: str) -> dict:
        """
        Parse H2H JSON data for head-to-head matches (copied from parser.py poisk_h2h_json_lichki function)
        """
        dannie_matchs = {"GROUP_LABEL": "", "ITEMS": []}
        stage = {'3': 'FINISHED', '5': 'CANCELLED', '4': 'Postponed', '11': 'After Penalties',
                '10': 'After extra time', '54': 'Those. defeat', '9': 'No-show'}
        result = {"l": "LOST", "w": "WIN", "d": "DRAW", "lo": "LOST", "wo": "WIN"}

        try:
            h2hs_homes = dannie_text.split('¬~')
            dannie_matchs["GROUP_LABEL"] = h2hs_homes[0]

            for h2h in h2hs_homes[1:]:
                match_item = {
                    "START_TIME": "", "EVENT_ID": "", "EVENT_NAME": "", "STAGE": "", "COUNTRY_ID": "", "COUNTRY_NAME": "",
                    "EVENT_ACRONYM": "", "HOME_PARTICIPANT": "", "HOME_PARTICIPANT_NAME_ONE": "", "HOME_PARTICIPANT_NAME_TWO": None,
                    "AWAY_PARTICIPANT": "", "AWAY_PARTICIPANT_NAME_ONE": "", "AWAY_PARTICIPANT_NAME_TWO": None, "CURRENT_RESULT": "",
                    "HOME_SCORE_FULL": "", "AWAY_SCORE_FULL": "", "HOME_IMAGES": [""], "AWAY_IMAGES": [""]
                }
                try:
                    match_item["EVENT_ID"] = h2h.split('KP÷')[1].split('¬')[0]
                    match_item["START_TIME"] = int(h2h.split('KC÷')[1].split('¬')[0])
                    match_item["EVENT_NAME"] = h2h.split('KF÷')[1].split('¬')[0]
                    try:
                        match_item["STAGE"] = stage[h2h.split('AC÷')[1].split('¬')[0]]
                    except:
                        pass

                    match_item["COUNTRY_ID"] = int(h2h.split('KG÷')[1].split('¬')[0])
                    match_item["COUNTRY_NAME"] = h2h.split('KH÷')[1].split('¬')[0]
                    match_item["EVENT_ACRONYM"] = h2h.split('KI÷')[1].split('¬')[0]
                    match_item["HOME_PARTICIPANT"] = h2h.split('KJ÷')[1].split('¬')[0]
                    match_item["HOME_PARTICIPANT_NAME_ONE"] = h2h.split('FH÷')[1].split('¬')[0]
                    match_item["AWAY_PARTICIPANT"] = h2h.split('KK÷')[1].split('¬')[0]
                    match_item["AWAY_PARTICIPANT_NAME_ONE"] = h2h.split('FK÷')[1].split('¬')[0]
                    match_item["CURRENT_RESULT"] = h2h.split('KL÷')[1].split('¬')[0]
                    match_item["HOME_SCORE_FULL"] = h2h.split('KU÷')[1].split('¬')[0]
                    match_item["AWAY_SCORE_FULL"] = h2h.split('KT÷')[1].split('¬')[0]
                    match_item["HOME_IMAGES"][0] = f"https://www.flashscore.com/res/image/data/{h2h.split('EC÷')[1].split('¬')[0]}"
                    match_item["AWAY_IMAGES"][0] = f"https://www.flashscore.com/res/image/data/{h2h.split('ED÷')[1].split('¬')[0]}"

                    dannie_matchs["ITEMS"].append(match_item)
                except:
                    pass
        except:
            pass

        return dannie_matchs

    def save_matches_in_ireland_format(self, matches: List[Match], league_name: str = None) -> bool:
        """
        Save all matches in Ireland format to a single JSON file

        Args:
            matches: List of Match objects
            league_name: Optional league name for filename

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            if not matches:
                logger.warning("No matches to save")
                return False

            # Create filename based on league name
            if league_name:
                safe_league_name = league_name.lower().replace(" ", "_").replace(":", "")
                filename = f"{safe_league_name}_stats.json"
            else:
                # Use first match's league name
                safe_league_name = matches[0].league_name.lower().replace(" ", "_").replace(":", "")
                filename = f"{safe_league_name}_stats.json"

            # Create full filepath
            filepath = f"/Users/<USER>/_1_2_flashscore_parser/{filename}"

            # Collect all matches data
            all_matches_data = {}

            for match in matches:
                try:
                    # Create match_info
                    match_info = {
                        "event_id": match.event_id,
                        "home_team": match.home_team.name,
                        "away_team": match.away_team.name,
                        "short_home": match.home_team.short_name,
                        "short_away": match.away_team.short_name,
                        "start_time": match.start_time.isoformat(),
                        "league": match.league_name,
                        "tournament_hash": match.tournament_hash,
                        "current_result": match.current_result,
                        "status": match.status,
                        "match_url": match.match_url
                    }

                    # Get H2H data
                    h2h_data = self._get_h2h_data_from_api(match.event_id, match.home_team.short_name, match.away_team.short_name)

                    # Add to collection
                    all_matches_data[match.event_id] = {
                        "match_info": match_info,
                        "h2h_data": h2h_data
                    }

                    # Small delay to avoid overwhelming API
                    time.sleep(0.1)

                except Exception as e:
                    logger.warning(f"Error processing match {match.event_id}: {e}")
                    continue

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(all_matches_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(all_matches_data)} matches in Ireland format to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving matches in Ireland format: {e}")
            return False

    def parse_and_save_matches_ireland_format(self, sport_id: int = 1, days_ahead: int = 7, league_name: str = None) -> Dict:
        """
        Parse upcoming matches and save them in Ireland format (like ireland_premier_division_stats.json)

        Args:
            sport_id: Sport ID (1=Football)
            days_ahead: Number of days to look ahead
            league_name: Optional league name for filename

        Returns:
            Dictionary with processing results
        """
        logger.info(f"Starting matches parsing for Ireland format, sport {sport_id}")

        # Get upcoming matches with whitelist filtering
        upcoming_matches = self.get_upcoming_matches(sport_id, days_ahead, apply_whitelist=True)

        if not upcoming_matches:
            logger.warning("No upcoming matches found")
            return {
                "total_matches": 0,
                "processed_matches": 0,
                "successful_saves": 0,
                "errors": 0
            }

        # Save all matches in Ireland format
        success = self.save_matches_in_ireland_format(upcoming_matches, league_name)

        results = {
            "total_matches": len(upcoming_matches),
            "processed_matches": len(upcoming_matches),
            "successful_saves": 1 if success else 0,
            "errors": 0 if success else 1,
            "success_rate": "100%" if success else "0%"
        }

        logger.info(f"Completed Ireland format processing: {'Success' if success else 'Failed'}")
        return results

    def parse_and_save_upcoming_matches(self, sport_id: int = 1, days_ahead: int = 7, output_dir: str = "matches") -> Dict:
        """
        Main method: Parse upcoming matches by whitelist and save each with statistics

        Args:
            sport_id: Sport ID (1=Football)
            days_ahead: Number of days to look ahead
            output_dir: Directory to save match files

        Returns:
            Dictionary with processing results
        """
        logger.info(f"Starting upcoming matches parsing for sport {sport_id}")

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Get upcoming matches with whitelist filtering
        upcoming_matches = self.get_upcoming_matches(sport_id, days_ahead, apply_whitelist=True)

        if not upcoming_matches:
            logger.warning("No upcoming matches found")
            return {
                "total_matches": 0,
                "processed_matches": 0,
                "successful_saves": 0,
                "errors": 0,
                "output_directory": output_dir
            }

        logger.info(f"Processing {len(upcoming_matches)} upcoming matches...")

        processed = 0
        successful_saves = 0
        errors = 0

        for match in upcoming_matches:
            try:
                # Get match statistics
                match_stats = self.get_match_statistics(match)

                if match_stats:
                    # Save match with statistics
                    if self.save_match_with_statistics(match_stats, output_dir):
                        successful_saves += 1
                    else:
                        errors += 1
                else:
                    errors += 1

                processed += 1

                # Add small delay to avoid overwhelming the API
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error processing match {match.event_id}: {e}")
                errors += 1
                processed += 1

        results = {
            "total_matches": len(upcoming_matches),
            "processed_matches": processed,
            "successful_saves": successful_saves,
            "errors": errors,
            "output_directory": output_dir,
            "success_rate": f"{(successful_saves/len(upcoming_matches)*100):.1f}%" if upcoming_matches else "0%"
        }

        logger.info(f"Completed processing: {successful_saves}/{len(upcoming_matches)} matches saved successfully")
        return results


def main():
    """Main function to demonstrate the parser with upcoming matches and statistics"""
    parser = FlashScoreParser()

    print("="*60)
    print("FlashScore Parser - Upcoming Matches (4 Whitelisted Leagues Only)")
    print("="*60)

    # Configure parser to use TOURNAMENT_ID mode for exact 4 leagues
    parser.update_whitelist_mode(WhitelistMode.TOURNAMENT_ID)
    parser.enable_whitelist(True)

    # Show whitelist configuration
    from whitelist import sp_good_ligs, league_details
    print(f"Whitelist Configuration:")
    print(f"  Mode: TOURNAMENT_ID (exact hash matching)")
    print(f"  Enabled: True")
    print(f"  Target Leagues: {len(sp_good_ligs)}")

    for hash_id in sp_good_ligs:
        if hash_id in league_details:
            league = league_details[hash_id]
            print(f"    - {league['name']} (Hash: {hash_id})")

    # Parse and save upcoming matches with statistics
    print(f"\n🚀 Starting upcoming matches parsing (4 leagues only)...")
    results = parser.parse_and_save_upcoming_matches(
        sport_id=SportType.FOOTBALL.value,
        days_ahead=7,
        output_dir="football_matches"
    )

    # Show results
    print(f"\n📊 Processing Results:")
    print(f"  Total upcoming matches found: {results['total_matches']}")
    print(f"  Matches processed: {results['processed_matches']}")
    print(f"  Successfully saved: {results['successful_saves']}")
    print(f"  Errors: {results['errors']}")
    print(f"  Success rate: {results['success_rate']}")
    print(f"  Output directory: {results['output_directory']}")

    if results['successful_saves'] > 0:
        print(f"\n✅ Successfully saved {results['successful_saves']} matches with statistics!")
        print(f"📁 Check the '{results['output_directory']}' folder for individual match files")
        print(f"💡 Each file is named {'{match_id}.json'} and contains:")
        print(f"   - Match details (teams, time, league)")
        print(f"   - Head-to-head statistics")
        print(f"   - Historical match data")
        print(f"   - Only from the 4 whitelisted leagues")
    else:
        print(f"\n❌ No matches were saved successfully")
        print(f"💡 This might mean no upcoming matches in the 4 target leagues")

    # Show sample of upcoming matches
    upcoming_matches = parser.get_upcoming_matches(SportType.FOOTBALL.value, days_ahead=3)
    if upcoming_matches:
        print(f"\n📅 Upcoming matches from whitelisted leagues (next 3 days):")
        for i, match in enumerate(upcoming_matches):
            print(f"  {i+1}. {match.home_team.name} vs {match.away_team.name}")
            print(f"     League: {match.league_name}")
            print(f"     Time: {match.start_time.strftime('%Y-%m-%d %H:%M')}")
            print(f"     Match ID: {match.event_id}")
            print(f"     Tournament Hash: {match.tournament_hash}")
            print()
    else:
        print(f"\n❌ No upcoming matches found in the 4 whitelisted leagues")

    print(f"🎯 Parser configured for ONLY 4 whitelisted leagues")
    print(f"📈 Each match file contains complete statistics and head-to-head data")


if __name__ == "__main__":
    main()
