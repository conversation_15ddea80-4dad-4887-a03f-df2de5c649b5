"""
Простой парсер на основе старого кода с нормальными именами
"""

import json
import time
import requests
from datetime import datetime
from parser import poisk_h2h_json, poisk_h2h_json_lichki
from league_whitelist_config import default_whitelist_manager


def get_connection(url):
    """Подключение к API"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/111.0',
        'Accept': '*/*',
        'Origin': 'https://www.flashscore.com',
        'Referer': 'https://www.flashscore.com/',
        'x-fsign': 'SW9D1eZo'
    }
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            return response
        else:
            print(f"HTTP {response.status_code} для {url}")
    except Exception as e:
        print(f"Ошибка запроса: {e}")
    return None


def parse_matches_from_day(sport_id, day_offset):
    """Парсинг матчей за день"""
    matches = []
    url = f'https://global.flashscore.ninja/2/x/feed/f_{sport_id}_{day_offset}_3_en_1'

    try:
        print(f"Запрос к: {url}")
        response = get_connection(url)
        if not response:
            print(f"Нет ответа от API для дня {day_offset}")
            return matches
            
        response.encoding = 'utf-8'
        page_text = response.text.split('~ZA÷')
        
        for text_section in page_text[1:]:
            country_id = text_section.split('ZB÷')[1].split('¬')[0]
            league = text_section.split('¬')[0]
            
            # Извлекаем tournament_hash для вайтлиста
            tournament_hash = ""
            lines = text_section.split('¬')
            for line in lines:
                if line.startswith('ZC÷'):
                    tournament_hash = line[3:]
                    break

            # Проверяем вайтлист по tournament_hash
            from whitelist import sp_good_ligs
            if tournament_hash not in sp_good_ligs:
                continue

            print(f"Найдена лига из вайтлиста: {league}, Hash: {tournament_hash}")
                
            games = text_section.split('~AA÷')
            for game in games[1:]:
                try:
                    status = game.split('AC÷')[1].split('¬')[0]
                    match_id = game.split('¬')[0]

                    # Фильтруем только матчи которые не начались
                    # Статусы начавшихся/завершенных матчей: 3, 5, 4, 11, 10, 54, 9, 12, 13, 38, 42
                    started_statuses = ['3', '5', '4', '11', '10', '54', '9', '12', '13', '38', '42']
                    if status in started_statuses:
                        continue
                    match_time = datetime.fromtimestamp(int(game.split('AD÷')[1].split('¬')[0])).strftime('%d.%m.%Y %H:%M')
                    home_team = game.split('AE÷')[1].split('¬')[0]
                    away_team = game.split('AF÷')[1].split('¬')[0]
                    
                    home_short = game.split('WM÷')[1].split('¬')[0]
                    away_short = game.split('WN÷')[1].split('¬')[0]
                    
                    try:
                        home_score = game.split('AG÷')[1].split('¬')[0]
                    except:
                        home_score = ''
                        
                    try:
                        away_score = game.split('AH÷')[1].split('¬')[0]
                    except:
                        away_score = ''
                    
                    match_url = f'https://www.flashscore.com/match/{match_id}/#/match-summary'
                    
                    match_data = [match_time, [league, int(country_id)], home_team, away_team, '', '', '', 
                                 home_score, away_score, '', status, match_url, '', home_short, away_short, 
                                 '', '', '', '', '', '', sport_id, match_id]
                    
                    if match_data not in matches:
                        matches.append(match_data)
                        
                except:
                    pass
    except:
        pass
        
    return matches


def get_h2h_data(match_id, home_short, away_short):
    """Получение H2H данных"""
    h2h_data = {"DATA": []}
    
    try:
        url = f'https://global.flashscore.ninja/2/x/feed/df_hh_1_{match_id}'
        response = get_connection(url)
        if response and response.text:
            all_data_text = response.text
            tab_names = all_data_text.split('~KA÷')
            data_sections = all_data_text.split('~KB÷')
            
            if len(data_sections) >= 7:
                # Overall данные
                overall_home = poisk_h2h_json(data_sections[1])
                overall_away = poisk_h2h_json(data_sections[2])
                overall_h2h = poisk_h2h_json_lichki(data_sections[3])
                
                overall_tab = {
                    "TAB_NAME": tab_names[1].split('¬')[0] if len(tab_names) > 1 else "Overall",
                    "GROUPS": [overall_home, overall_away, overall_h2h]
                }
                h2h_data["DATA"].append(overall_tab)
                
                # Домашние матчи
                home_matches = poisk_h2h_json(data_sections[4])
                home_h2h = poisk_h2h_json_lichki(data_sections[5])
                home_tab = {
                    "TAB_NAME": f"{home_short} - Home",
                    "GROUPS": [home_matches, home_h2h]
                }
                h2h_data["DATA"].append(home_tab)
                
                # Гостевые матчи
                away_matches = poisk_h2h_json(data_sections[6])
                away_tab = {
                    "TAB_NAME": f"{away_short} - Away",
                    "GROUPS": [away_matches, home_h2h]
                }
                h2h_data["DATA"].append(away_tab)
                
    except Exception as e:
        print(f"Ошибка H2H для {match_id}: {e}")
    
    return h2h_data


def save_matches_to_json(matches_list, folder_path):
    """Сохранение каждого матча в отдельный JSON файл"""
    try:
        import os

        # Создаем папку если не существует
        os.makedirs(folder_path, exist_ok=True)

        saved_count = 0

        for match in matches_list:
            match_id = match[-1]
            home_short = match[13]
            away_short = match[14]

            # match_info
            match_info = {
                "event_id": match_id,
                "home_team": match[2],
                "away_team": match[3],
                "short_home": home_short,
                "short_away": away_short,
                "start_time": datetime.strptime(match[0], '%d.%m.%Y %H:%M').isoformat(),
                "league": match[1][0],
                "tournament_hash": "",
                "current_result": f"{match[7]}:{match[8]}" if match[7] and match[8] else "",
                "status": match[10],
                "match_url": match[11]
            }

            # h2h_data
            h2h_data = get_h2h_data(match_id, home_short, away_short)

            # Данные одного матча
            match_data = {
                match_id: {
                    "match_info": match_info,
                    "h2h_data": h2h_data
                }
            }

            # Сохраняем в отдельный файл
            filename = f"{match_id}.json"
            filepath = os.path.join(folder_path, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(match_data, f, ensure_ascii=False, indent=2)

            saved_count += 1
            print(f"Сохранен матч {saved_count}: {match_id}.json")
            time.sleep(0.1)

        print(f"Всего сохранено {saved_count} матчей в папку {folder_path}")
        return True

    except Exception as e:
        print(f"Ошибка сохранения: {e}")
        return False


def main():
    """Основная функция"""
    print("Запуск парсера...")
    
    # Получаем матчи на 2 дня вперед
    all_matches = []
    for day in range(0, 2):  # 2 дня вперед
        matches = parse_matches_from_day(1, day)  # 1 = футбол
        all_matches.extend(matches)
        print(f"День {day}: найдено {len(matches)} матчей")
    
    print(f"Всего найдено {len(all_matches)} матчей по вайтлисту")
    
    if all_matches:
        folder_path = "/Users/<USER>/_1_2_flashscore_parser/matches"
        save_matches_to_json(all_matches, folder_path)
    else:
        print("Нет матчей для сохранения")


if __name__ == "__main__":
    main()
