#!/usr/bin/env python3
"""
Test main flashscore_parser.py
"""

from flashscore_parser import FlashScoreParser, SportType

def main():
    """Test the main parser"""
    print("="*60)
    print("Testing Main FlashScore Parser")
    print("="*60)
    
    # Create parser instance
    parser = FlashScoreParser()
    
    print("🚀 Starting main parser test...")
    
    # Parse and save matches individually (3 days ahead, only unstarted matches)
    results = parser.parse_and_save_individual_matches(
        sport_id=SportType.FOOTBALL.value,
        days_ahead=3,
        folder_name="matches"
    )
    
    # Show results
    print(f"\n📊 Results:")
    print(f"  Total matches: {results['total_matches']}")
    print(f"  Saved: {results['successful_saves']}")
    print(f"  Errors: {results['errors']}")
    print(f"  Success rate: {results['success_rate']}")

if __name__ == "__main__":
    main()
