#!/usr/bin/env python3
"""
Comprehensive usage examples for the whitelist filtering system
Demonstrates various ways to use the modernized FlashScore parser with filtering
"""

import json
from datetime import datetime
from flashscore_parser import FlashScoreParser, SportType
from league_whitelist_config import (
    LeagueWhitelistManager, 
    WhitelistMode,
    get_whitelisted_country_ids,
    get_whitelisted_league_names
)


def example_1_basic_usage():
    """Example 1: Basic usage with default whitelist"""
    print("="*60)
    print("EXAMPLE 1: Basic Usage with Default Whitelist")
    print("="*60)
    
    # Initialize parser (whitelist enabled by default)
    parser = FlashScoreParser()
    
    # Get today's football matches (filtered by default)
    matches = parser.get_today_matches(SportType.FOOTBALL.value)
    
    print(f"Found {len(matches)} whitelisted football matches")
    
    # Save in both formats
    parser.save_matches_to_json(matches, "example1_modern.json")
    parser.save_matches_to_legacy_format(matches, "example1_legacy.json")
    
    print("✅ Files saved: example1_modern.json, example1_legacy.json")


def example_2_disable_whitelist():
    """Example 2: Disable whitelist to get all matches"""
    print("\n" + "="*60)
    print("EXAMPLE 2: Disable Whitelist to Get All Matches")
    print("="*60)
    
    parser = FlashScoreParser()
    
    # Disable whitelist filtering
    parser.enable_whitelist(False)
    
    # Get all matches without filtering
    all_matches = parser.get_today_matches(SportType.FOOTBALL.value)
    
    print(f"Found {len(all_matches)} total matches (no filtering)")
    
    # Save unfiltered data
    parser.save_matches_to_json(all_matches, "example2_all_matches.json")
    
    print("✅ File saved: example2_all_matches.json")


def example_3_custom_whitelist():
    """Example 3: Create custom whitelist configuration"""
    print("\n" + "="*60)
    print("EXAMPLE 3: Custom Whitelist Configuration")
    print("="*60)
    
    # Create custom whitelist manager
    custom_config = {
        "mode": WhitelistMode.COUNTRY_ID,
        "enabled": True,
        "description": "Only major European leagues"
    }
    
    custom_manager = LeagueWhitelistManager(custom_config)
    
    # Update whitelist to only include major European countries
    major_european_countries = {17, 15, 16, 23, 18, 19, 20, 21}  # England, Spain, Germany, Italy, France, Netherlands, Portugal, Belgium
    custom_manager.update_whitelist(
        country_ids=major_european_countries,
        sport_id=SportType.FOOTBALL.value
    )
    
    # Create parser with custom whitelist
    parser = FlashScoreParser(whitelist_manager=custom_manager)
    
    matches = parser.get_today_matches(SportType.FOOTBALL.value)
    
    print(f"Found {len(matches)} matches from major European leagues")
    
    # Show which countries are represented
    countries = set(match.country_id for match in matches)
    print(f"Countries represented: {sorted(countries)}")
    
    parser.save_matches_to_json(matches, "example3_european_only.json")
    print("✅ File saved: example3_european_only.json")


def example_4_different_modes():
    """Example 4: Test different whitelist modes"""
    print("\n" + "="*60)
    print("EXAMPLE 4: Different Whitelist Modes")
    print("="*60)
    
    parser = FlashScoreParser()
    
    modes_results = {}
    
    for mode in [WhitelistMode.COUNTRY_ID, WhitelistMode.LEAGUE_NAME, WhitelistMode.COMBINED]:
        print(f"\nTesting mode: {mode.value}")
        
        parser.update_whitelist_mode(mode)
        matches = parser.get_today_matches(SportType.FOOTBALL.value)
        
        modes_results[mode.value] = len(matches)
        print(f"  Matches found: {len(matches)}")
        
        # Save mode-specific results
        filename = f"example4_{mode.value}_mode.json"
        parser.save_matches_to_json(matches, filename)
        print(f"  ✅ Saved: {filename}")
    
    print(f"\n📊 Mode Comparison:")
    for mode, count in modes_results.items():
        print(f"  {mode}: {count} matches")


def example_5_multi_sport_filtering():
    """Example 5: Multi-sport filtering"""
    print("\n" + "="*60)
    print("EXAMPLE 5: Multi-Sport Filtering")
    print("="*60)
    
    parser = FlashScoreParser()
    
    sports = [
        (SportType.FOOTBALL.value, "Football"),
        (SportType.BASKETBALL.value, "Basketball"),
        (SportType.TENNIS.value, "Tennis")
    ]
    
    for sport_id, sport_name in sports:
        print(f"\n{sport_name} (ID: {sport_id}):")
        
        # Get whitelist stats for this sport
        stats = parser.get_whitelist_stats(sport_id)
        print(f"  Whitelisted countries: {stats['whitelisted_countries']}")
        print(f"  Whitelisted leagues: {stats['whitelisted_leagues']}")
        
        # Get matches
        matches = parser.get_today_matches(sport_id)
        print(f"  Matches found: {len(matches)}")
        
        if matches:
            filename = f"example5_{sport_name.lower()}_matches.json"
            parser.save_matches_to_json(matches, filename)
            print(f"  ✅ Saved: {filename}")


def example_6_filtering_analysis():
    """Example 6: Detailed filtering analysis"""
    print("\n" + "="*60)
    print("EXAMPLE 6: Detailed Filtering Analysis")
    print("="*60)
    
    parser = FlashScoreParser()
    
    # Get detailed filtering summary
    summary = parser.get_filtered_leagues_summary(SportType.FOOTBALL.value)
    
    print(f"📊 Filtering Analysis:")
    print(f"  Total leagues: {summary['total_leagues']}")
    print(f"  Whitelisted leagues: {summary['whitelisted_leagues']}")
    print(f"  Filtered leagues: {summary['filtered_leagues']}")
    print(f"  Total matches: {summary['total_matches']}")
    print(f"  Whitelisted matches: {summary['whitelisted_matches']}")
    print(f"  Filtered matches: {summary['filtered_matches']}")
    
    # Save detailed analysis
    with open("example6_filtering_analysis.json", "w", encoding="utf-8") as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print("✅ Detailed analysis saved: example6_filtering_analysis.json")


def example_7_legacy_compatibility():
    """Example 7: Legacy format compatibility"""
    print("\n" + "="*60)
    print("EXAMPLE 7: Legacy Format Compatibility")
    print("="*60)
    
    parser = FlashScoreParser()
    
    # Get matches
    matches = parser.get_today_matches(SportType.FOOTBALL.value)
    
    if matches:
        # Save in legacy format (compatible with original parser)
        parser.save_matches_to_legacy_format(matches, "example7_legacy_compatible.json")
        
        # Load and verify the legacy format
        with open("example7_legacy_compatible.json", "r", encoding="utf-8") as f:
            legacy_data = json.load(f)
        
        print(f"Legacy format verification:")
        print(f"  Format: {legacy_data['format']}")
        print(f"  Total matches: {legacy_data['total_matches']}")
        print(f"  Whitelist applied: {legacy_data['whitelist_applied']}")
        
        # Show structure of first match
        if legacy_data['matches']:
            first_match = legacy_data['matches'][0]
            print(f"  Sample match structure: {len(first_match)} fields")
            print(f"  Match: {first_match[2]} vs {first_match[3]}")  # team1 vs team2
            print(f"  League: {first_match[1][0]}")  # league name
            print(f"  Country ID: {first_match[1][1]}")  # country ID
        
        print("✅ Legacy format saved: example7_legacy_compatible.json")


def example_8_date_range_filtering():
    """Example 8: Date range filtering"""
    print("\n" + "="*60)
    print("EXAMPLE 8: Date Range Filtering")
    print("="*60)
    
    parser = FlashScoreParser()
    
    # Get matches for multiple days
    days = [-1, 0, 1]  # Yesterday, today, tomorrow
    matches = parser.get_matches_for_date_range(SportType.FOOTBALL.value, days)
    
    print(f"Found {len(matches)} whitelisted matches across {len(days)} days")
    
    # Group by date
    by_date = {}
    for match in matches:
        date_str = match.start_time.strftime('%Y-%m-%d')
        if date_str not in by_date:
            by_date[date_str] = []
        by_date[date_str].append(match)
    
    for date_str, date_matches in sorted(by_date.items()):
        print(f"  {date_str}: {len(date_matches)} matches")
    
    parser.save_matches_to_json(matches, "example8_date_range.json")
    print("✅ File saved: example8_date_range.json")


def main():
    """Run all usage examples"""
    print("FlashScore Parser - Whitelist Filtering Usage Examples")
    print("Demonstrating various ways to use the filtering system")
    
    try:
        example_1_basic_usage()
        example_2_disable_whitelist()
        example_3_custom_whitelist()
        example_4_different_modes()
        example_5_multi_sport_filtering()
        example_6_filtering_analysis()
        example_7_legacy_compatibility()
        example_8_date_range_filtering()
        
        print("\n" + "="*60)
        print("ALL EXAMPLES COMPLETED SUCCESSFULLY")
        print("="*60)
        print("✅ All usage examples completed!")
        print("\nGenerated files:")
        print("  - example1_modern.json, example1_legacy.json")
        print("  - example2_all_matches.json")
        print("  - example3_european_only.json")
        print("  - example4_*_mode.json (3 files)")
        print("  - example5_*_matches.json (3 files)")
        print("  - example6_filtering_analysis.json")
        print("  - example7_legacy_compatible.json")
        print("  - example8_date_range.json")
        print("\n🎯 Key Features Demonstrated:")
        print("  ✅ Default whitelist filtering")
        print("  ✅ Enable/disable filtering")
        print("  ✅ Custom whitelist configuration")
        print("  ✅ Multiple filtering modes")
        print("  ✅ Multi-sport support")
        print("  ✅ Detailed filtering analysis")
        print("  ✅ Legacy format compatibility")
        print("  ✅ Date range filtering")
        
    except Exception as e:
        print(f"❌ Examples failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
