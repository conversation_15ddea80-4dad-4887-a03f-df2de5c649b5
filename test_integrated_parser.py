#!/usr/bin/env python3
"""
Test integrated parser with individual match saving
"""

from flashscore_parser import FlashScoreParser, SportType

def main():
    """Test the integrated parser"""
    print("="*60)
    print("Testing Integrated FlashScore Parser")
    print("="*60)
    
    # Create parser instance
    parser = FlashScoreParser()
    
    print("🚀 Starting individual matches parsing...")
    
    # Parse and save matches individually (only unstarted matches)
    results = parser.parse_and_save_individual_matches(
        sport_id=SportType.FOOTBALL.value,
        days_ahead=2,
        folder_name="matches"
    )
    
    # Show results
    print(f"\n📊 Processing Results:")
    print(f"  Total matches found: {results['total_matches']}")
    print(f"  Matches processed: {results['processed_matches']}")
    print(f"  Successfully saved: {results['successful_saves']}")
    print(f"  Errors: {results['errors']}")
    print(f"  Success rate: {results['success_rate']}")
    
    if results['successful_saves'] > 0:
        print(f"\n✅ Successfully saved {results['successful_saves']} individual match files!")
        print(f"📁 Check folder: /Users/<USER>/_1_2_flashscore_parser/matches/")
    else:
        print(f"\n❌ No matches were saved")
    
    print(f"\n🎯 Only unstarted matches from whitelist saved as individual JSON files")

if __name__ == "__main__":
    main()
