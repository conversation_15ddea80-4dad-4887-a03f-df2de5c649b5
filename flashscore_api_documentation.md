# FlashScore API Documentation

## Overview
This document provides comprehensive documentation for the FlashScore API endpoints, data structures, and field mappings based on analysis of the original parser code.

## API Endpoints

### Base URLs
- Primary: `https://global.flashscore.ninja`
- Backup: `https://local-global.flashscore.ninja`

### Authentication Headers
```
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/111.0
Accept: */*
Origin: https://www.flashscore.com
Referer: https://www.flashscore.com/
x-fsign: SW9D1eZo
x-geoip2-city-name: London
x-geoip2-country-code: GB
```

### Main Endpoints

#### 1. Match Data Endpoint
**URL Pattern:** `/2/x/feed/f_{sport_id}_{day_offset}_3_en_1`

**Parameters:**
- `sport_id`: Sport type identifier (see Sport IDs section)
- `day_offset`: Days from today (-1=yesterday, 0=today, 1=tomorrow, etc.)

**Example:** 
- Today's football matches: `/2/x/feed/f_1_0_3_en_1`
- Tomorrow's basketball: `/2/x/feed/f_3_1_3_en_1`

#### 2. Head-to-Head Data Endpoint
**URL Pattern:** `/2/x/feed/df_hh_1_{match_id}`

**Parameters:**
- `match_id`: Unique match identifier from FlashScore

#### 3. Betting Odds Endpoint
**URL Pattern:** `/2/x/feed/df_od_1_{match_id}_`

**Parameters:**
- `match_id`: Unique match identifier from FlashScore

## Sport IDs

| Sport | ID | Notes |
|-------|----|----|
| Football (Soccer) | 1 | Most popular, extensive data |
| Tennis | 2 | Individual sport |
| Basketball | 3 | Team sport |
| Hockey | 4 | Team sport |
| American Football | 5 | Team sport |
| Baseball | 6 | Team sport |
| Handball | 7 | Team sport |
| Volleyball | 12 | Team sport |
| Badminton | 21 | Individual/doubles |
| Table Tennis | 25 | Individual/doubles |

## Data Field Mappings

### Match Status Codes
| Code | Meaning | Description |
|------|---------|-------------|
| 3 | FINISHED | Match completed |
| 5 | CANCELLED | Match cancelled |
| 4 | POSTPONED | Match postponed |
| 11 | AFTER_PENALTIES | Finished after penalty shootout |
| 10 | AFTER_EXTRA_TIME | Finished after extra time |
| 54 | TECHNICAL_DEFEAT | Technical defeat |
| 9 | NO_SHOW | Team didn't show up |
| 12 | FIRST_HALF | Currently in first half |
| 13 | SECOND_HALF | Currently in second half |
| 38 | HALF_TIME | Half time break |
| 42 | AWAITING_UPDATES | Waiting for updates |

### Match Data Field Codes

#### Basic Match Information
| Code | Field | Description | Example |
|------|-------|-------------|---------|
| AA÷ | Match ID | Unique match identifier | `AA÷8KIT2SzJ` |
| AC÷ | Status | Match status code | `AC÷3` |
| AD÷ | Start Time | Unix timestamp | `AD÷1723852800` |
| AE÷ | Home Team | Home team name | `AE÷Barcelona` |
| AF÷ | Away Team | Away team name | `AF÷Real Madrid` |
| AG÷ | Home Score | Home team score | `AG÷2` |
| AH÷ | Away Score | Away team score | `AH÷1` |

#### Team Short Names and Images
| Code | Field | Description |
|------|-------|-------------|
| WM÷ | Home Short Name | Home team abbreviation |
| WN÷ | Away Short Name | Away team abbreviation |
| EC÷ | Home Team Image | Home team image identifier |
| ED÷ | Away Team Image | Away team image identifier |

#### League Information
| Code | Field | Description |
|------|-------|-------------|
| ZA÷ | League Name | League/competition name |
| ZB÷ | Country ID | Country identifier |

### Head-to-Head Data Field Codes

#### H2H Match Information
| Code | Field | Description |
|------|-------|-------------|
| KP÷ | H2H Match ID | Historical match ID |
| KC÷ | H2H Start Time | Historical match timestamp |
| KF÷ | H2H Event Name | Historical match league |
| KG÷ | H2H Country ID | Historical match country |
| KH÷ | H2H Country Name | Historical match country name |
| KI÷ | H2H Event Acronym | League abbreviation |
| KJ÷ | H2H Home Team | Historical home team |
| KK÷ | H2H Away Team | Historical away team |
| KL÷ | H2H Result | Historical match result |
| KU÷ | H2H Home Score | Historical home score |
| KT÷ | H2H Away Score | Historical away score |
| KN÷ | H2H Outcome | Win/Loss/Draw indicator |
| KS÷ | H2H Team Mark | Home/Away indicator |
| FH÷ | H2H Home Name | Full home team name |
| FK÷ | H2H Away Name | Full away team name |

### Betting Odds Field Codes

#### 1X2 Odds (Win/Draw/Win)
| Code | Field | Description |
|------|-------|-------------|
| OA÷ | Market Type | Betting market name (1X2, O/U, etc.) |
| OB÷ | Market Subtype | Market variation (Full Time, etc.) |
| OC÷ | Line Value | Handicap/Total line value |
| OD÷ | Bookmaker | Bookmaker name |
| OE÷ | Odds Group | Group of odds for market |
| XA÷ | Home Win Odds | 1X2 home win odds |
| XB÷ | Draw Odds | 1X2 draw odds |
| XC÷ | Away Win Odds | 1X2 away win odds |

#### Odds Movement Indicators
Odds can include movement indicators in brackets:
- `[u]` - Odds went up (green arrow)
- `[d]` - Odds went down (red arrow)
- Numbers after brackets show the change amount

**Example:** `1.85[d]:1.75` means odds dropped from 1.85 to 1.75

### Result Indicators (H2H)
| Code | Meaning |
|------|---------|
| w | Win |
| l | Loss |
| d | Draw |
| wo | Win (alternative) |
| lo | Loss (alternative) |

## Data Extraction Patterns

### Parsing Match Data
1. Split response by `~ZA÷` to get league sections
2. For each league: extract country ID with `ZB÷` pattern
3. Split by `~AA÷` to get individual matches
4. Extract match fields using specific codes

### Parsing H2H Data
1. Split response by `~KB÷` to get different H2H sections
2. Each section contains different types of historical data
3. Split by `¬~` to get individual historical matches
4. Extract match details using KX÷ patterns

### Parsing Betting Odds
1. Split by `~OA÷` to get different betting markets
2. For each market, split by `~OB÷` for subtypes
3. Split by `~OE÷` to get bookmaker odds
4. Extract specific odds using XA÷, XB÷, XC÷ patterns

## Common Data Processing Steps

### 1. Date/Time Handling
- API returns Unix timestamps
- Convert using: `datetime.fromtimestamp(timestamp)`
- Format for display: `strftime('%d.%m.%Y %H:%M')`

### 2. Team Name Cleaning
- Remove asterisks (*) from team names
- Strip whitespace and parenthetical information
- Handle special characters and encoding

### 3. Score Processing
- Scores may be empty for upcoming matches
- Handle different score formats (regular time, penalties, etc.)
- Parse current result vs final result

### 4. Image URL Construction
- Base URL: `https://www.flashscore.com/res/image/data/`
- Append image identifier from EC÷ or ED÷ fields
- Handle missing or invalid image identifiers

## Error Handling Recommendations

1. **Network Errors**: Implement retry logic with exponential backoff
2. **Parsing Errors**: Use try-catch blocks for each field extraction
3. **Missing Data**: Provide default values for optional fields
4. **Rate Limiting**: Add delays between requests
5. **Authentication**: Monitor for authentication failures and refresh headers

## Testing Strategy

### Phase 1: Basic Connectivity
1. Test connection to main endpoints
2. Verify header authentication
3. Check response format

### Phase 2: Data Parsing
1. Start with football (sport ID 1) - most reliable
2. Test with today's matches (day offset 0)
3. Verify field extraction accuracy

### Phase 3: Comprehensive Testing
1. Test multiple sports
2. Test different day offsets
3. Test H2H and odds endpoints
4. Validate data consistency

## Rate Limiting and Best Practices

1. **Request Frequency**: Max 1 request per 100ms
2. **Concurrent Requests**: Limit to 20 simultaneous connections
3. **User-Agent Rotation**: Consider rotating user agents for large-scale scraping
4. **Error Monitoring**: Log all failed requests for analysis
5. **Data Caching**: Cache responses to reduce API load

## Sample API Responses

### Match Data Response Structure
```
~ZA÷Premier League¬ZB÷17¬~AA÷8KIT2SzJ¬AC÷3¬AD÷1723852800¬AE÷Arsenal¬AF÷Chelsea¬AG÷2¬AH÷1¬WM÷ARS¬WN÷CHE¬
```

### H2H Response Structure
```
~KB÷Overall¬~KP÷match123¬KC÷1723000000¬KF÷Premier League¬KJ÷Arsenal¬KK÷Chelsea¬KL÷2:1¬KN÷w¬
```

### Betting Odds Response Structure
```
~OA÷1X2¬~OB÷Full Time¬~OE÷¬XA÷2.10¬XB÷3.40¬XC÷3.20¬
```
