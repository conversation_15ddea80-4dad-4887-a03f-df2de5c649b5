"""
Сохранение матчей в формате как в ireland_premier_division_stats.json
Использует функции из parser.py
"""

import json
import time
import logging
from typing import List, Dict
from parser import poisk_h2h_json, poisk_h2h_json_lichki
import requests

logger = logging.getLogger(__name__)


class IrelandFormatSaver:
    """Сохранение матчей в формате как в ireland_premier_division_stats.json"""
    
    def __init__(self):
        self.timeout = 10
    
    def save_matches_to_file(self, matches: List, filepath: str) -> bool:
        """
        Сохранить матчи в файл в формате как ireland_premier_division_stats.json
        
        Args:
            matches: Список матчей
            filepath: Путь к файлу для сохранения
            
        Returns:
            True если успешно сохранено
        """
        try:
            if not matches:
                logger.warning("Нет матчей для сохранения")
                return False
            
            # Собираем все данные матчей
            all_matches_data = {}
            
            for match in matches:
                try:
                    # Создаем match_info
                    match_info = {
                        "event_id": match.event_id,
                        "home_team": match.home_team.name,
                        "away_team": match.away_team.name,
                        "short_home": match.home_team.short_name,
                        "short_away": match.away_team.short_name,
                        "start_time": match.start_time.isoformat(),
                        "league": match.league_name,
                        "tournament_hash": match.tournament_hash,
                        "current_result": match.current_result,
                        "status": match.status,
                        "match_url": match.match_url
                    }
                    
                    # Получаем H2H данные
                    h2h_data = self._get_h2h_data(match.event_id, match.home_team.short_name, match.away_team.short_name)
                    
                    # Добавляем в коллекцию
                    all_matches_data[match.event_id] = {
                        "match_info": match_info,
                        "h2h_data": h2h_data
                    }
                    
                    # Небольшая задержка
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.warning(f"Ошибка обработки матча {match.event_id}: {e}")
                    continue
            
            # Сохраняем в файл
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(all_matches_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Сохранено {len(all_matches_data)} матчей в {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка сохранения матчей: {e}")
            return False
    
    def _get_h2h_data(self, event_id: str, short_home: str, short_away: str) -> dict:
        """
        Получить H2H данные используя функции из parser.py
        
        Args:
            event_id: ID матча
            short_home: Короткое имя домашней команды
            short_away: Короткое имя гостевой команды
            
        Returns:
            H2H данные в нужном формате
        """
        try:
            # Используем тот же URL что и в parser.py
            url = f'https://global.flashscore.ninja/2/x/feed/df_hh_1_{event_id}'
            
            response = requests.get(url, timeout=self.timeout)
            if response.status_code == 200 and response.text:
                all_dannie_text = response.text
                t_names = all_dannie_text.split('~KA÷')
                dannie_text_all = all_dannie_text.split('~KB÷')
                
                if len(dannie_text_all) >= 7:
                    # Используем функции из parser.py точно как там
                    dannie_overall_home = poisk_h2h_json(dannie_text_all[1])
                    dannie_overall_away = poisk_h2h_json(dannie_text_all[2]) 
                    dannie_overall_lichki = poisk_h2h_json_lichki(dannie_text_all[3])
                    
                    overall_json = {
                        "TAB_NAME": t_names[1].split('¬')[0] if len(t_names) > 1 else "Overall",
                        "GROUPS": [dannie_overall_home, dannie_overall_away, dannie_overall_lichki]
                    }
                    
                    h2h_json = {"DATA": [overall_json]}
                    
                    # Добавляем домашние и гостевые данные
                    dannie_home_home = poisk_h2h_json(dannie_text_all[4])
                    dannie_home_lichki = poisk_h2h_json_lichki(dannie_text_all[5])
                    home_home_json = {
                        "TAB_NAME": f"{short_home} - Home",
                        "GROUPS": [dannie_home_home, dannie_home_lichki]
                    }
                    h2h_json["DATA"].append(home_home_json)
                    
                    dannie_away_away = poisk_h2h_json(dannie_text_all[6])
                    away_away_json = {
                        "TAB_NAME": f"{short_away} - Away", 
                        "GROUPS": [dannie_away_away, dannie_home_lichki]
                    }
                    h2h_json["DATA"].append(away_away_json)
                    
                    return h2h_json
                    
        except Exception as e:
            logger.warning(f"Ошибка получения H2H данных: {e}")
            
        return {"DATA": []}
    
    def create_filename(self, league_name: str = None, matches: List = None) -> str:
        """
        Создать имя файла в формате @/Users/<USER>/_1_2_flashscore_parser/league_name_stats.json
        
        Args:
            league_name: Название лиги
            matches: Список матчей (для получения названия лиги)
            
        Returns:
            Полный путь к файлу
        """
        if league_name:
            safe_league_name = league_name.lower().replace(" ", "_").replace(":", "")
        elif matches and len(matches) > 0:
            safe_league_name = matches[0].league_name.lower().replace(" ", "_").replace(":", "")
        else:
            safe_league_name = "matches"
        
        filename = f"{safe_league_name}_stats.json"
        return f"/Users/<USER>/_1_2_flashscore_parser/{filename}"
