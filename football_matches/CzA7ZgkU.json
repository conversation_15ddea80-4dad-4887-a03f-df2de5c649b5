{"timestamp": "2025-06-16T15:12:30.936264", "match_id": "CzA7ZgkU", "match": {"event_id": "CzA7ZgkU", "start_time": "2025-06-16T18:00:00", "sport_id": 1, "league_name": "BRAZIL: Serie B Superbet", "country_id": 39, "country_name": "", "home_team": {"name": "Chapecoense-SC", "short_name": "CHA", "image_url": ""}, "away_team": {"name": "Ferroviaria", "short_name": "FER", "image_url": ""}, "status": "1", "home_score": "", "away_score": "", "current_result": "", "match_url": "https://www.flashscore.com/match/CzA7ZgkU/#/match-summary", "league_index": 7, "tournament_hash": "vF2C38ii"}, "head_to_head": {"overall_home": [{"home_team": "Novorizontino", "away_team": "Chapecoense-SC", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Amazonas", "score": "4:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Chapecoense-SC", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON><PERSON>", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Athletico-PR", "away_team": "Chapecoense-SC", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Cricium<PERSON>", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Vila Nova FC", "away_team": "Chapecoense-SC", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Athletic Club", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Paysandu PA", "away_team": "Chapecoense-SC", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Coritiba", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "CRB", "away_team": "Chapecoense-SC", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Chapecoense-SC", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Joinville", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Brusque", "away_team": "Chapecoense-SC", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Joinville", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Chapecoense-SC", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Figueirense", "away_team": "Chapecoense-SC", "score": "2:4", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Santa Catarina", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Barra FC", "away_team": "Chapecoense-SC", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Cricium<PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Concordia", "away_team": "Chapecoense-SC", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Brusque", "away_team": "Chapecoense-SC", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Caravaggio", "away_team": "Chapecoense-SC", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Concordia", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Mirassol", "away_team": "Chapecoense-SC", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Coritiba", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Sport Recife", "away_team": "Chapecoense-SC", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Concordia", "away_team": "Chapecoense-SC", "score": "4:1", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Novorizontino", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "Brusque", "away_team": "Chapecoense-SC", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Concordia", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "0:4", "result": "LOST", "date": ""}, {"home_team": "Barra FC", "away_team": "Chapecoense-SC", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON>", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Joinville", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Paysandu PA", "away_team": "Chapecoense-SC", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Nacao", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Operario-PR", "away_team": "Chapecoense-SC", "score": "3:2", "result": "LOST", "date": ""}, {"home_team": "Concordia", "away_team": "Chapecoense-SC", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Amazonas", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Chapecoense-SC", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Vila Nova FC", "away_team": "Chapecoense-SC", "score": "3:2", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Figueirense", "away_team": "Chapecoense-SC", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Ponte Preta", "away_team": "Chapecoense-SC", "score": "0:2", "result": "WIN", "date": ""}], "overall_away": [{"home_team": "America MG", "away_team": "Ferroviaria", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Botafogo SP", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Ferroviaria", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Ferroviaria", "away_team": "<PERSON><PERSON>", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Novorizontino", "away_team": "Ferroviaria", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Coritiba", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Ferroviaria", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Atletico GO", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Amazonas", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Remo", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Ituano", "away_team": "Ferroviaria", "score": "4:1", "result": "ll", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Ituano", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "<PERSON><PERSON><PERSON>", "score": "3:1", "result": "WIN", "date": ""}, {"home_team": "Rio Claro", "away_team": "Ferroviaria", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Sao Bento", "away_team": "Ferroviaria", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Portuguesa Santista", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Piracicaba", "away_team": "Ferroviaria", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Sao Jose EC", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Linense", "away_team": "Ferroviaria", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Ferroviaria", "away_team": "<PERSON>", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Primavera", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Juventus-SP", "away_team": "Ferroviaria", "score": "4:3", "result": "LOST", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Taubate", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Capivariano", "away_team": "Ferroviaria", "score": "3:1", "result": "LOST", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Ituano", "score": "3:1", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Oeste", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Votuporanguense", "away_team": "Ferroviaria", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Botafogo SP", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Ypiranga FC", "score": "4:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Ferroviaria", "score": "3:2", "result": "LOST", "date": ""}, {"home_team": "Athletic Club", "away_team": "Ferroviaria", "score": "3:0", "result": "LOST", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Athletic Club", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "<PERSON><PERSON><PERSON>", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Ypiranga FC", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Botafogo PB", "away_team": "Ferroviaria", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Volta Redonda", "score": "5:1", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Athletic Club", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Ypiranga FC", "away_team": "Ferroviaria", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "<PERSON><PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Sao Bernardo", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Remo", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Ferroviaria", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "EC Sao Jose", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Ferroviario", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "SER Caxias", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Aparecidense", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Tombense", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Figueirense", "away_team": "Ferroviaria", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Ferroviaria", "away_team": "Confianca", "score": "1:0", "result": "WIN", "date": ""}], "head_to_head": [], "home_home": [{"home_team": "Chapecoense-SC", "away_team": "Amazonas", "score": "4:0", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON><PERSON>", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Cricium<PERSON>", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Athletic Club", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Coritiba", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Joinville", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Joinville", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Santa Catarina", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Cricium<PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Concordia", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Coritiba", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Novorizontino", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Concordia", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "0:4", "result": "LOST", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON>", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Joinville", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Nacao", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "Amazonas", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON><PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Chapecoense-SC", "away_team": "<PERSON><PERSON>", "score": "2:1", "result": "WIN", "date": ""}], "away_away": [{"home_team": "America MG", "away_team": "Ferroviaria", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Ferroviaria", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Novorizontino", "away_team": "Ferroviaria", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Ferroviaria", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Volta Redonda", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Amazonas", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Ituano", "away_team": "Ferroviaria", "score": "4:1", "result": "ll", "date": ""}, {"home_team": "Rio Claro", "away_team": "Ferroviaria", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Sao Bento", "away_team": "Ferroviaria", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Piracicaba", "away_team": "Ferroviaria", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Linense", "away_team": "Ferroviaria", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Juventus-SP", "away_team": "Ferroviaria", "score": "4:3", "result": "LOST", "date": ""}, {"home_team": "Capivariano", "away_team": "Ferroviaria", "score": "3:1", "result": "LOST", "date": ""}, {"home_team": "Votuporanguense", "away_team": "Ferroviaria", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Ferroviaria", "score": "3:2", "result": "LOST", "date": ""}, {"home_team": "Athletic Club", "away_team": "Ferroviaria", "score": "3:0", "result": "LOST", "date": ""}, {"home_team": "Ypiranga FC", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Botafogo PB", "away_team": "Ferroviaria", "score": "2:0", "result": "LOST", "date": ""}, {"home_team": "Ypiranga FC", "away_team": "Ferroviaria", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Sao Bernardo", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Ferroviaria", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Ferroviario", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "SER Caxias", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Tombense", "away_team": "Ferroviaria", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Figueirense", "away_team": "Ferroviaria", "score": "0:1", "result": "WIN", "date": ""}]}, "betting_odds": null}