#!/usr/bin/env python3
"""
Test script for Ireland format parser
"""

from flashscore_parser import FlashScoreParser, SportType
from league_whitelist_config import WhitelistMode

def main():
    """Test the Ireland format parser"""
    print("="*60)
    print("Testing FlashScore Parser - Ireland Format")
    print("="*60)
    
    # Create parser instance
    parser = FlashScoreParser()
    
    # Configure for football only
    parser.update_whitelist_mode(WhitelistMode.TOURNAMENT_ID)
    parser.enable_whitelist(True)
    
    print("🚀 Starting matches parsing in Ireland format...")
    
    # Parse and save matches in Ireland format
    results = parser.parse_and_save_matches_ireland_format(
        sport_id=SportType.FOOTBALL.value,
        days_ahead=7,
        league_name="football_matches"
    )
    
    # Show results
    print(f"\n📊 Processing Results:")
    print(f"  Total matches found: {results['total_matches']}")
    print(f"  Matches processed: {results['processed_matches']}")
    print(f"  Successfully saved: {results['successful_saves']}")
    print(f"  Errors: {results['errors']}")
    print(f"  Success rate: {results['success_rate']}")
    
    if results['successful_saves'] > 0:
        print(f"\n✅ Successfully saved matches in Ireland format!")
        print(f"📁 Check the file: /Users/<USER>/_1_2_flashscore_parser/football_matches_stats.json")
    else:
        print(f"\n❌ No matches were saved successfully")
    
    print(f"\n🎯 Parser configured for Ireland format like ireland_premier_division_stats.json")

if __name__ == "__main__":
    main()
