#!/usr/bin/env python3
"""
Test script for the modernized FlashScore parser
Demonstrates step-by-step testing as requested
"""

import json
from datetime import datetime
from flashscore_parser import FlashScoreParser, SportType


def print_separator(title: str):
    """Print a nice separator for test sections"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)


def test_step_1_leagues():
    """Step 1: Test retrieving available leagues for today"""
    print_separator("STEP 1: Testing League Retrieval for Football Today")
    
    parser = FlashScoreParser()
    
    # Test with Football (sport-1) as requested
    sport_id = SportType.FOOTBALL.value
    print(f"Testing with Sport ID: {sport_id} (Football)")
    
    leagues = parser.get_leagues_for_today(sport_id)
    
    if leagues:
        print(f"\n✅ SUCCESS: Found {len(leagues)} leagues for today")
        print("\nTop 10 leagues:")
        for i, (league_name, details) in enumerate(list(leagues.items())[:10]):
            print(f"{i+1:2d}. {league_name}")
            for detail in details:
                print(f"    {detail}")
        
        # Save leagues to JSON
        leagues_data = {
            "timestamp": datetime.now().isoformat(),
            "sport_id": sport_id,
            "sport_name": "Football",
            "total_leagues": len(leagues),
            "leagues": leagues
        }
        
        with open("today_football_leagues.json", "w", encoding="utf-8") as f:
            json.dump(leagues_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 Leagues data saved to: today_football_leagues.json")
        return True
    else:
        print("❌ FAILED: No leagues found")
        return False


def test_step_2_matches():
    """Step 2: Test retrieving matches for today"""
    print_separator("STEP 2: Testing Match Retrieval for Football Today")
    
    parser = FlashScoreParser()
    
    # Test with Football (sport-1) as requested
    sport_id = SportType.FOOTBALL.value
    matches = parser.get_today_matches(sport_id)
    
    if matches:
        print(f"✅ SUCCESS: Found {len(matches)} matches for today")
        
        # Group matches by status
        status_counts = {}
        for match in matches:
            status = match.status
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print("\nMatches by status:")
        for status, count in sorted(status_counts.items()):
            print(f"  {status}: {count} matches")
        
        # Show sample matches
        print("\nSample matches (first 5):")
        for i, match in enumerate(matches[:5]):
            print(f"\n{i+1}. {match.home_team.name} vs {match.away_team.name}")
            print(f"   League: {match.league_name}")
            print(f"   Time: {match.start_time.strftime('%H:%M')}")
            print(f"   Status: {match.status}")
            if match.current_result:
                print(f"   Score: {match.current_result}")
            print(f"   Match ID: {match.event_id}")
        
        # Save matches to JSON
        parser.save_matches_to_json(matches, "today_football_matches_detailed.json")
        print(f"\n📁 Detailed matches saved to: today_football_matches_detailed.json")
        return matches
    else:
        print("❌ FAILED: No matches found")
        return []


def test_step_3_specific_league(matches):
    """Step 3: Test filtering matches by specific league"""
    print_separator("STEP 3: Testing League-Specific Match Filtering")
    
    if not matches:
        print("❌ No matches available for filtering")
        return
    
    # Group matches by league
    leagues = {}
    for match in matches:
        league = match.league_name
        if league not in leagues:
            leagues[league] = []
        leagues[league].append(match)
    
    # Find a league with multiple matches
    target_league = None
    for league, league_matches in leagues.items():
        if len(league_matches) >= 2:
            target_league = league
            break
    
    if target_league:
        league_matches = leagues[target_league]
        print(f"✅ Selected league: {target_league}")
        print(f"   Found {len(league_matches)} matches in this league")
        
        print(f"\nAll matches in {target_league}:")
        for i, match in enumerate(league_matches):
            print(f"{i+1}. {match.home_team.name} vs {match.away_team.name}")
            print(f"   Time: {match.start_time.strftime('%H:%M')}")
            print(f"   Status: {match.status}")
            if match.current_result:
                print(f"   Score: {match.current_result}")
        
        # Save league-specific data
        league_data = {
            "timestamp": datetime.now().isoformat(),
            "league_name": target_league,
            "total_matches": len(league_matches),
            "matches": [
                {
                    "event_id": match.event_id,
                    "home_team": match.home_team.name,
                    "away_team": match.away_team.name,
                    "start_time": match.start_time.isoformat(),
                    "status": match.status,
                    "current_result": match.current_result,
                    "match_url": match.match_url
                }
                for match in league_matches
            ]
        }
        
        filename = f"league_{target_league.replace(':', '_').replace(' ', '_')}.json"
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(league_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 League data saved to: {filename}")
    else:
        print("❌ No league found with multiple matches")


def test_step_4_api_exploration():
    """Step 4: Test API exploration capabilities"""
    print_separator("STEP 4: Testing API Exploration Capabilities")
    
    parser = FlashScoreParser()
    
    # Test different sports
    sports_to_test = [
        (SportType.FOOTBALL.value, "Football"),
        (SportType.BASKETBALL.value, "Basketball"),
        (SportType.TENNIS.value, "Tennis")
    ]
    
    api_summary = {
        "timestamp": datetime.now().isoformat(),
        "sports_tested": []
    }
    
    for sport_id, sport_name in sports_to_test:
        print(f"\nTesting {sport_name} (ID: {sport_id})...")
        
        matches = parser.get_today_matches(sport_id)
        leagues = parser.get_leagues_for_today(sport_id)
        
        sport_data = {
            "sport_id": sport_id,
            "sport_name": sport_name,
            "total_matches": len(matches),
            "total_leagues": len(leagues),
            "sample_leagues": list(leagues.keys())[:5] if leagues else []
        }
        
        api_summary["sports_tested"].append(sport_data)
        
        print(f"  ✅ {sport_name}: {len(matches)} matches, {len(leagues)} leagues")
    
    # Save API exploration results
    with open("api_exploration_summary.json", "w", encoding="utf-8") as f:
        json.dump(api_summary, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 API exploration summary saved to: api_exploration_summary.json")


def test_step_5_data_validation():
    """Step 5: Test data validation and quality"""
    print_separator("STEP 5: Testing Data Validation and Quality")
    
    parser = FlashScoreParser()
    matches = parser.get_today_matches(SportType.FOOTBALL.value)
    
    if not matches:
        print("❌ No matches available for validation")
        return
    
    # Validation checks
    validation_results = {
        "total_matches": len(matches),
        "valid_event_ids": 0,
        "valid_timestamps": 0,
        "valid_team_names": 0,
        "matches_with_scores": 0,
        "matches_with_urls": 0,
        "unique_leagues": set(),
        "status_distribution": {},
        "issues": []
    }
    
    for match in matches:
        # Check event ID
        if match.event_id and len(match.event_id) > 5:
            validation_results["valid_event_ids"] += 1
        else:
            validation_results["issues"].append(f"Invalid event ID: {match.event_id}")
        
        # Check timestamp
        if match.start_time:
            validation_results["valid_timestamps"] += 1
        
        # Check team names
        if match.home_team.name and match.away_team.name:
            validation_results["valid_team_names"] += 1
        
        # Check scores
        if match.current_result:
            validation_results["matches_with_scores"] += 1
        
        # Check URLs
        if match.match_url and "flashscore.com" in match.match_url:
            validation_results["matches_with_urls"] += 1
        
        # Track leagues
        validation_results["unique_leagues"].add(match.league_name)
        
        # Track status
        status = match.status
        validation_results["status_distribution"][status] = \
            validation_results["status_distribution"].get(status, 0) + 1
    
    # Convert set to list for JSON serialization
    validation_results["unique_leagues"] = list(validation_results["unique_leagues"])
    validation_results["total_unique_leagues"] = len(validation_results["unique_leagues"])
    
    # Calculate percentages
    total = validation_results["total_matches"]
    print(f"Data Quality Report:")
    print(f"  Total matches analyzed: {total}")
    print(f"  Valid event IDs: {validation_results['valid_event_ids']}/{total} ({validation_results['valid_event_ids']/total*100:.1f}%)")
    print(f"  Valid timestamps: {validation_results['valid_timestamps']}/{total} ({validation_results['valid_timestamps']/total*100:.1f}%)")
    print(f"  Valid team names: {validation_results['valid_team_names']}/{total} ({validation_results['valid_team_names']/total*100:.1f}%)")
    print(f"  Matches with scores: {validation_results['matches_with_scores']}/{total} ({validation_results['matches_with_scores']/total*100:.1f}%)")
    print(f"  Valid URLs: {validation_results['matches_with_urls']}/{total} ({validation_results['matches_with_urls']/total*100:.1f}%)")
    print(f"  Unique leagues: {validation_results['total_unique_leagues']}")
    
    print(f"\nStatus distribution:")
    for status, count in sorted(validation_results["status_distribution"].items()):
        print(f"  {status}: {count} matches")
    
    if validation_results["issues"]:
        print(f"\nIssues found: {len(validation_results['issues'])}")
        for issue in validation_results["issues"][:5]:  # Show first 5 issues
            print(f"  - {issue}")
    
    # Save validation results
    with open("data_validation_report.json", "w", encoding="utf-8") as f:
        json.dump(validation_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📁 Validation report saved to: data_validation_report.json")


def main():
    """Run all test steps"""
    print_separator("FlashScore Parser - Comprehensive Testing")
    print("Starting step-by-step testing as requested...")
    print("Focus: Football (спорт-1) for today's date")
    
    try:
        # Step 1: Test league retrieval
        leagues_success = test_step_1_leagues()
        
        if leagues_success:
            # Step 2: Test match retrieval
            matches = test_step_2_matches()
            
            if matches:
                # Step 3: Test specific league filtering
                test_step_3_specific_league(matches)
                
                # Step 4: Test API exploration
                test_step_4_api_exploration()
                
                # Step 5: Test data validation
                test_step_5_data_validation()
                
                print_separator("TESTING COMPLETED SUCCESSFULLY")
                print("✅ All test steps completed successfully!")
                print("\nGenerated files:")
                print("  - today_football_leagues.json")
                print("  - today_football_matches_detailed.json")
                print("  - league_*.json")
                print("  - api_exploration_summary.json")
                print("  - data_validation_report.json")
                print("\nNext steps:")
                print("  1. Review the generated JSON files")
                print("  2. Open test_interface.html in your browser")
                print("  3. Test with different sports and date ranges")
                print("  4. Implement betting odds and H2H data if needed")
            else:
                print("❌ Testing stopped: No matches found")
        else:
            print("❌ Testing stopped: No leagues found")
            
    except Exception as e:
        print(f"❌ Testing failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
