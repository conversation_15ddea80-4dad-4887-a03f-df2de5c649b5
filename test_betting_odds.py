#!/usr/bin/env python3
"""
Test script for betting odds functionality
Demonstrates how to extract betting odds from FlashScore API
"""

import json
from flashscore_parser import FlashScoreParser, SportType


def test_betting_odds_api():
    """Test betting odds API endpoint"""
    print("="*60)
    print(" Testing Betting Odds API")
    print("="*60)
    
    parser = FlashScoreParser()
    
    # Get some matches first
    matches = parser.get_today_matches(SportType.FOOTBALL.value)
    
    if not matches:
        print("❌ No matches found to test betting odds")
        return
    
    # Test with first few matches
    test_matches = matches[:3]
    
    print(f"Testing betting odds for {len(test_matches)} matches:\n")
    
    odds_results = []
    
    for i, match in enumerate(test_matches):
        print(f"{i+1}. {match.home_team.name} vs {match.away_team.name}")
        print(f"   Match ID: {match.event_id}")
        print(f"   League: {match.league_name}")
        print(f"   Status: {match.status}")
        
        # Try to get betting odds
        odds_data = parser.api.get_betting_odds(match.event_id)
        
        if odds_data:
            print(f"   ✅ Betting odds data retrieved ({len(odds_data)} characters)")
            
            # Basic parsing to show structure
            if '~OA÷' in odds_data:
                markets = odds_data.split('~OA÷')
                print(f"   📊 Found {len(markets)-1} betting markets")
                
                # Show available markets
                market_names = []
                for market in markets[1:6]:  # Show first 5 markets
                    try:
                        market_name = market.split('¬')[0]
                        market_names.append(market_name)
                    except:
                        pass
                
                if market_names:
                    print(f"   🎯 Markets: {', '.join(market_names)}")
            
            odds_results.append({
                "match_id": match.event_id,
                "home_team": match.home_team.name,
                "away_team": match.away_team.name,
                "odds_data_length": len(odds_data),
                "has_odds": True
            })
        else:
            print(f"   ❌ No betting odds data available")
            odds_results.append({
                "match_id": match.event_id,
                "home_team": match.home_team.name,
                "away_team": match.away_team.name,
                "odds_data_length": 0,
                "has_odds": False
            })
        
        print()
    
    # Save results
    with open("betting_odds_test_results.json", "w", encoding="utf-8") as f:
        json.dump({
            "timestamp": parser.api.session.headers.get('User-Agent', 'Unknown'),
            "total_matches_tested": len(test_matches),
            "matches_with_odds": sum(1 for r in odds_results if r["has_odds"]),
            "results": odds_results
        }, f, ensure_ascii=False, indent=2)
    
    print("📁 Results saved to: betting_odds_test_results.json")
    
    # Summary
    matches_with_odds = sum(1 for r in odds_results if r["has_odds"])
    print(f"\n📊 Summary:")
    print(f"   Matches tested: {len(test_matches)}")
    print(f"   Matches with odds: {matches_with_odds}")
    print(f"   Success rate: {matches_with_odds/len(test_matches)*100:.1f}%")


def test_h2h_data_api():
    """Test head-to-head data API endpoint"""
    print("="*60)
    print(" Testing Head-to-Head Data API")
    print("="*60)
    
    parser = FlashScoreParser()
    
    # Get some matches first
    matches = parser.get_today_matches(SportType.FOOTBALL.value)
    
    if not matches:
        print("❌ No matches found to test H2H data")
        return
    
    # Test with first few matches
    test_matches = matches[:3]
    
    print(f"Testing H2H data for {len(test_matches)} matches:\n")
    
    h2h_results = []
    
    for i, match in enumerate(test_matches):
        print(f"{i+1}. {match.home_team.name} vs {match.away_team.name}")
        print(f"   Match ID: {match.event_id}")
        print(f"   League: {match.league_name}")
        
        # Try to get H2H data
        h2h_data = parser.api.get_head_to_head_data(match.event_id)
        
        if h2h_data:
            print(f"   ✅ H2H data retrieved ({len(h2h_data)} characters)")
            
            # Basic parsing to show structure
            if '~KB÷' in h2h_data:
                sections = h2h_data.split('~KB÷')
                print(f"   📊 Found {len(sections)-1} H2H sections")
                
                # Look for historical matches
                if '¬~' in h2h_data:
                    historical_matches = h2h_data.split('¬~')
                    print(f"   🏆 Found ~{len(historical_matches)-1} historical matches")
            
            h2h_results.append({
                "match_id": match.event_id,
                "home_team": match.home_team.name,
                "away_team": match.away_team.name,
                "h2h_data_length": len(h2h_data),
                "has_h2h": True
            })
        else:
            print(f"   ❌ No H2H data available")
            h2h_results.append({
                "match_id": match.event_id,
                "home_team": match.home_team.name,
                "away_team": match.away_team.name,
                "h2h_data_length": 0,
                "has_h2h": False
            })
        
        print()
    
    # Save results
    with open("h2h_test_results.json", "w", encoding="utf-8") as f:
        json.dump({
            "timestamp": parser.api.session.headers.get('User-Agent', 'Unknown'),
            "total_matches_tested": len(test_matches),
            "matches_with_h2h": sum(1 for r in h2h_results if r["has_h2h"]),
            "results": h2h_results
        }, f, ensure_ascii=False, indent=2)
    
    print("📁 Results saved to: h2h_test_results.json")
    
    # Summary
    matches_with_h2h = sum(1 for r in h2h_results if r["has_h2h"])
    print(f"\n📊 Summary:")
    print(f"   Matches tested: {len(test_matches)}")
    print(f"   Matches with H2H: {matches_with_h2h}")
    print(f"   Success rate: {matches_with_h2h/len(test_matches)*100:.1f}%")


def demonstrate_api_structure():
    """Demonstrate the API data structure"""
    print("="*60)
    print(" API Data Structure Demonstration")
    print("="*60)
    
    parser = FlashScoreParser()
    
    # Get one match for detailed analysis
    matches = parser.get_today_matches(SportType.FOOTBALL.value)
    
    if not matches:
        print("❌ No matches found for demonstration")
        return
    
    # Find a finished match for better data
    finished_match = None
    for match in matches:
        if match.status == "FINISHED":
            finished_match = match
            break
    
    if not finished_match:
        finished_match = matches[0]  # Use first match if no finished ones
    
    print(f"Analyzing match: {finished_match.home_team.name} vs {finished_match.away_team.name}")
    print(f"Match ID: {finished_match.event_id}")
    print(f"Status: {finished_match.status}")
    print()
    
    # Get raw API data
    print("1. Raw Match Data Structure:")
    match_data = parser.api.get_matches_for_day(SportType.FOOTBALL.value, 0)
    if match_data and finished_match.event_id in match_data:
        # Find this specific match in the data
        match_section = ""
        for section in match_data.split('~AA÷'):
            if finished_match.event_id in section:
                match_section = section[:200] + "..." if len(section) > 200 else section
                break
        
        if match_section:
            print(f"   Raw data sample: {match_section}")
            print()
    
    print("2. Betting Odds Data Structure:")
    odds_data = parser.api.get_betting_odds(finished_match.event_id)
    if odds_data:
        odds_sample = odds_data[:300] + "..." if len(odds_data) > 300 else odds_data
        print(f"   Raw odds sample: {odds_sample}")
    else:
        print("   No odds data available")
    print()
    
    print("3. Head-to-Head Data Structure:")
    h2h_data = parser.api.get_head_to_head_data(finished_match.event_id)
    if h2h_data:
        h2h_sample = h2h_data[:300] + "..." if len(h2h_data) > 300 else h2h_data
        print(f"   Raw H2H sample: {h2h_sample}")
    else:
        print("   No H2H data available")
    print()
    
    # Save demonstration data
    demo_data = {
        "match_info": {
            "event_id": finished_match.event_id,
            "home_team": finished_match.home_team.name,
            "away_team": finished_match.away_team.name,
            "status": finished_match.status,
            "league": finished_match.league_name
        },
        "api_endpoints_tested": {
            "match_data": bool(match_data),
            "betting_odds": bool(odds_data),
            "head_to_head": bool(h2h_data)
        },
        "data_samples": {
            "match_data_length": len(match_data) if match_data else 0,
            "odds_data_length": len(odds_data) if odds_data else 0,
            "h2h_data_length": len(h2h_data) if h2h_data else 0
        }
    }
    
    with open("api_structure_demo.json", "w", encoding="utf-8") as f:
        json.dump(demo_data, f, ensure_ascii=False, indent=2)
    
    print("📁 Demonstration data saved to: api_structure_demo.json")


def main():
    """Run all betting odds and H2H tests"""
    print("FlashScore API - Advanced Features Testing")
    print("Testing betting odds and head-to-head data extraction")
    print()
    
    try:
        # Test betting odds
        test_betting_odds_api()
        print()
        
        # Test H2H data
        test_h2h_data_api()
        print()
        
        # Demonstrate API structure
        demonstrate_api_structure()
        
        print("="*60)
        print(" ADVANCED TESTING COMPLETED")
        print("="*60)
        print("✅ All advanced features tested!")
        print("\nGenerated files:")
        print("  - betting_odds_test_results.json")
        print("  - h2h_test_results.json")
        print("  - api_structure_demo.json")
        print("\nNext steps:")
        print("  1. Review the API data structure")
        print("  2. Implement full odds parsing if needed")
        print("  3. Add H2H data processing")
        print("  4. Integrate with your betting analysis")
        
    except Exception as e:
        print(f"❌ Testing failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
