{"timestamp": "2025-06-16T15:12:31.957578", "match_id": "0ryPhjsQ", "match": {"event_id": "0ryPhjsQ", "start_time": "2025-06-17T15:00:00", "sport_id": 1, "league_name": "WORLD: FIFA Club World Cup", "country_id": 8, "country_name": "", "home_team": {"name": "River Plate", "short_name": "RIV", "image_url": ""}, "away_team": {"name": "Urawa Reds", "short_name": "URA", "image_url": ""}, "status": "1", "home_score": "", "away_score": "", "current_result": "", "match_url": "https://www.flashscore.com/match/0ryPhjsQ/#/match-summary", "league_index": 48, "tournament_hash": "KOtwQCtI"}, "head_to_head": {"overall_home": [{"home_team": "River Plate", "away_team": "U. de Deportes", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Platense", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "Ind. del Valle", "score": "6:2", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Barracas Central", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Barcelona SC", "away_team": "River Plate", "score": "2:3", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Velez Sarsfield", "score": "4:1", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Boca Juniors", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Ind. del Valle", "away_team": "River Plate", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Gimnasia L.P.", "away_team": "River Plate", "score": "0:3", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Talleres Cordoba", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Barcelona SC", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Sarmiento Jun<PERSON>", "away_team": "River Plate", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "U. de Deportes", "away_team": "River Plate", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Rosario Central", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Ciudad Bolivar", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "River Plate", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "<PERSON><PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Talleres Cordoba", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "Estudiantes L.P.", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "San Martin S.J.", "away_team": "River Plate", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "River Plate", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Independiente", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "San Lorenzo", "away_team": "River Plate", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Instituto", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Platense", "away_team": "River Plate", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Mexico", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "U. De Chile", "away_team": "River Plate", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Racing Club", "away_team": "River Plate", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "Rosario Central", "score": "4:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "San Lorenzo", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Estudiantes L.P.", "away_team": "River Plate", "score": "1:2", "result": "WIN", "date": ""}, {"home_team": "Ind. Rivadavia", "away_team": "River Plate", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "Barracas Central", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Instituto", "away_team": "River Plate", "score": "2:3", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Banfield", "score": "3:1", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Atletico-MG", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Defensa y Justicia", "away_team": "River Plate", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Atletico-MG", "away_team": "River Plate", "score": "3:0", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "Velez Sarsfield", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Platense", "away_team": "River Plate", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Talleres Cordoba", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "Colo Colo", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Boca Juniors", "away_team": "River Plate", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Colo Colo", "away_team": "River Plate", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "<PERSON><PERSON><PERSON>", "score": "4:1", "result": "WIN", "date": ""}, {"home_team": "Independiente", "away_team": "River Plate", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Newells Old Boys", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Talleres Cordoba", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Gimnasia L.P.", "away_team": "River Plate", "score": "1:1", "result": "DRAW", "date": ""}], "overall_away": [{"home_team": "Urawa Reds", "away_team": "Yokohama FC", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Cerezo <PERSON>", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Nagoya Grampus", "away_team": "Urawa Reds", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Kawasaki Frontale", "away_team": "Urawa Reds", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Urawa Reds", "away_team": "FC Tokyo", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Albirex Niigata", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Gamba Osaka", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Urawa Reds", "away_team": "<PERSON><PERSON><PERSON>", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Sanfrecce Hiroshima", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Yokohama F. Marinos", "score": "3:1", "result": "WIN", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Kyoto", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Avispa Fukuoka", "away_team": "Urawa Reds", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Urawa Reds", "away_team": "<PERSON><PERSON><PERSON>", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "Cerezo <PERSON>", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Kashima Antlers", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Urawa Reds", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Kashiwa <PERSON>", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Kyoto", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Vissel Kobe", "away_team": "Urawa Reds", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Nagoya Grampus", "score": "5:6", "result": "LOST", "date": ""}, {"home_team": "Gamba Osaka", "away_team": "Urawa Reds", "score": "2:4", "result": "WIN", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Okinawa SV", "score": "5:0", "result": "WIN", "date": ""}, {"home_team": "Urawa Reds", "away_team": "<PERSON><PERSON><PERSON>", "score": "3:2", "result": "WIN", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Albirex Niigata", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Avispa Fukuoka", "away_team": "Urawa Reds", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Kawasaki Frontale", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Sanfrecce Hiroshima", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Yokohama F. Marinos", "away_team": "Urawa Reds", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Kashiwa <PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Cerezo <PERSON>", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "Vissel Kobe", "away_team": "Urawa Reds", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Urawa Reds", "away_team": "FC Tokyo", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "Gamba Osaka", "away_team": "Urawa Reds", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Kashima Antlers", "away_team": "Urawa Reds", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Newcastle", "score": "1:4", "result": "LOST", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Hokkaido Consadole Sapporo", "score": "3:4", "result": "LOST", "date": ""}, {"home_team": "Kyoto", "away_team": "Urawa Reds", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Urawa Reds", "away_team": "<PERSON><PERSON><PERSON>", "score": "2:3", "result": "LOST", "date": ""}, {"home_team": "Urawa Reds", "away_team": "<PERSON><PERSON><PERSON>", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "Nagoya Grampus", "away_team": "Urawa Reds", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Kashima Antlers", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Cerezo <PERSON>", "away_team": "Urawa Reds", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Urawa Reds", "away_team": "Vissel Kobe", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Urawa Reds", "away_team": "<PERSON><PERSON><PERSON>", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "1:0", "result": "LOST", "date": ""}], "head_to_head": [], "home_home": [{"home_team": "River Plate", "away_team": "U. de Deportes", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Platense", "score": "1:2", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "Ind. del Valle", "score": "6:2", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Barracas Central", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Velez Sarsfield", "score": "4:1", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Boca Juniors", "score": "2:1", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Talleres Cordoba", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Barcelona SC", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Rosario Central", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Ciudad Bolivar", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "<PERSON><PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Talleres Cordoba", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "Estudiantes L.P.", "score": "0:2", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "<PERSON><PERSON>", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Independiente", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Instituto", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Mexico", "score": "2:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Rosario Central", "score": "4:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "San Lorenzo", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Barracas Central", "score": "3:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Banfield", "score": "3:1", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Atletico-MG", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Velez Sarsfield", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Talleres Cordoba", "score": "0:1", "result": "LOST", "date": ""}, {"home_team": "River Plate", "away_team": "Colo Colo", "score": "1:0", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "<PERSON><PERSON><PERSON>", "score": "4:1", "result": "WIN", "date": ""}, {"home_team": "River Plate", "away_team": "Newells Old Boys", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "River Plate", "away_team": "Talleres Cordoba", "score": "2:1", "result": "WIN", "date": ""}], "away_away": [{"home_team": "Nagoya Grampus", "away_team": "Urawa Reds", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Kawasaki Frontale", "away_team": "Urawa Reds", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Albirex Niigata", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "0:2", "result": "WIN", "date": ""}, {"home_team": "Avispa Fukuoka", "away_team": "Urawa Reds", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Cerezo <PERSON>", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Kashima Antlers", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Kyoto", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Vissel Kobe", "away_team": "Urawa Reds", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Gamba Osaka", "away_team": "Urawa Reds", "score": "2:4", "result": "WIN", "date": ""}, {"home_team": "Avispa Fukuoka", "away_team": "Urawa Reds", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Yokohama F. Marinos", "away_team": "Urawa Reds", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "Vissel Kobe", "away_team": "Urawa Reds", "score": "1:0", "result": "LOST", "date": ""}, {"home_team": "Gamba Osaka", "away_team": "Urawa Reds", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "2:2", "result": "DRAW", "date": ""}, {"home_team": "Kashima Antlers", "away_team": "Urawa Reds", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "<PERSON><PERSON>", "away_team": "Urawa Reds", "score": "1:1", "result": "DRAW", "date": ""}, {"home_team": "Kyoto", "away_team": "Urawa Reds", "score": "0:0", "result": "DRAW", "date": ""}, {"home_team": "Nagoya Grampus", "away_team": "Urawa Reds", "score": "0:1", "result": "WIN", "date": ""}, {"home_team": "Cerezo <PERSON>", "away_team": "Urawa Reds", "score": "2:1", "result": "LOST", "date": ""}, {"home_team": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "away_team": "Urawa Reds", "score": "1:0", "result": "LOST", "date": ""}]}, "betting_odds": null}